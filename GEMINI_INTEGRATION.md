# Integración de Gemini AI para Procesamiento de Lenguaje Natural

## 🎯 Objetivo

Este proyecto integra Google Gemini AI para procesar mensajes de WhatsApp en lenguaje natural argentino y extraer información de gastos automáticamente.

## 🚀 Características

- **Procesamiento de Lenguaje Natural**: Entiende frases como "Compré en el chino por 2500"
- **Jerga Argentina**: Reconoce términos como "mangos", "chino", "bondi", "doc"
- **Categorización Automática**: Clasifica gastos en 7 categorías principales
- **Retrocompatibilidad**: Mantiene soporte para formato estructurado anterior
- **Métricas y Logging**: Monitoreo completo de performance y costos
- **Fallback Inteligente**: Sistema robusto de respaldos

## 📋 Configuración

### Variables de Entorno

Agregar al archivo `.env`:

```env
# Gemini AI
GEMINI_API_KEY=tu_api_key_de_gemini_aqui

# WhatsApp (existentes)
WHATSAPP_TOKEN=tu_token_whatsapp
WHATSAPP_NUMBER_ID=tu_numero_id
VERIFY_TOKEN=tu_verify_token
```

### Instalación

```bash
npm install @google/generative-ai
npm run build
npm start
```

## 💬 Ejemplos de Uso

### Lenguaje Natural (Nuevo)

| Entrada | Resultado |
|---------|-----------|
| "Compré en el chino por 2500" | 💰 $2500, 🏪 Supermercado, "Compra en almacén" |
| "Gasté 1500 mangos en el bondi" | 💰 $1500, 🚌 Transporte, "Viaje en colectivo" |
| "Me salió 3400 el super de hoy" | 💰 $3400, 🏪 Supermercado, "Compra en supermercado" |
| "Fui al doc, me cobró 8000" | 💰 $8000, 🏥 Salud, "Consulta médica" |

### Formato Estructurado (Compatible)

| Entrada | Resultado |
|---------|-----------|
| "4000 pesos una coca cola" | 💰 $4000, 🥤 Alimentos, "una coca cola" |
| "2500 transporte" | 💰 $2500, 🚌 Transporte, "transporte" |

## 🏗️ Arquitectura

### Módulos Principales

```
src/
├── ai/
│   ├── gemini.service.ts      # Servicio principal de IA
│   ├── logging.service.ts     # Métricas y logging
│   ├── ai.module.ts          # Módulo de IA
│   └── interfaces/
│       └── expense-data.interface.ts  # Tipos y interfaces
└── webhook/
    ├── webhook.service.ts     # Lógica de procesamiento
    └── webhook.controller.ts  # Endpoints + métricas
```

### Flujo de Procesamiento

1. **Recepción**: Mensaje llega via WhatsApp webhook
2. **Detección**: Sistema determina si es natural o estructurado
3. **Procesamiento**: 
   - Natural → Gemini AI
   - Estructurado → Lógica anterior
4. **Validación**: Verificación de confianza y datos
5. **Respuesta**: Confirmación al usuario

## 📊 Categorías Soportadas

| Categoría | Palabras Clave |
|-----------|----------------|
| **Supermercado** | chino, super, almacén, verdulería, carnicería |
| **Transporte** | bondi, colectivo, taxi, uber, nafta, subte |
| **Comida** | restaurant, delivery, café, almuerzo, cena |
| **Entretenimiento** | cine, teatro, boliche, juegos |
| **Salud** | doc, médico, farmacia, dentista, clínica |
| **Servicios** | luz, gas, internet, celular, peluquería |
| **Otros** | cualquier gasto no clasificado |

## 🔧 Monitoreo y Métricas

### Endpoint de Métricas

```
GET /webhook/metrics
```

**Respuesta:**
```json
{
  "status": "success",
  "data": {
    "totalRequests": 25,
    "successfulRequests": 23,
    "failedRequests": 2,
    "successRate": "92.0%",
    "averageConfidence": 0.87,
    "averageProcessingTime": "1250ms",
    "categoriesUsed": {
      "Supermercado": 8,
      "Transporte": 5,
      "Comida": 7,
      "Salud": 3
    }
  }
}
```

### Logs Automáticos

El sistema registra automáticamente:
- ✅ Requests exitosos/fallidos
- ⏱️ Tiempos de procesamiento
- 🎯 Niveles de confianza
- 💰 Estimación de costos
- 📈 Resumen cada 10 requests

## 🧪 Testing

### Tests Automatizados

```bash
# Tests unitarios
npm run test

# Tests de integración
npm run test -- test/integration/gemini.test.ts
```

### Prueba Manual

```bash
node scripts/test-gemini.js
```

## 🛡️ Sistema de Fallbacks

1. **Gemini Falla** → Lógica estructurada anterior
2. **Confianza Baja** (< 0.6) → Solicitar aclaración
3. **Error de Parsing** → Mensaje de ayuda
4. **API Timeout** → Respuesta de error amigable

## 💡 Jerga Argentina Soportada

| Término | Significado | Ejemplo |
|---------|-------------|---------|
| mangos, lucas | pesos | "Gasté 2000 mangos" |
| chino | almacén/supermercado | "Fui al chino" |
| bondi | colectivo/autobús | "Tomé el bondi" |
| doc | doctor | "Fui al doc" |
| nafta | combustible | "Cargué nafta" |

## 🔒 Consideraciones de Seguridad

- ✅ API Key en variables de entorno
- ✅ Validación de datos de entrada
- ✅ Rate limiting implícito de Gemini
- ✅ No almacenamiento de datos sensibles
- ✅ Logs sin información personal

## 💸 Costos Estimados

- **Gemini Pro**: ~$7 USD por millón de tokens
- **Promedio por request**: ~150 tokens
- **Costo por mensaje**: ~$0.001 USD
- **1000 mensajes/mes**: ~$1 USD

*Los costos se monitorean automáticamente en las métricas*

## 🐛 Troubleshooting

### Error: "GEMINI_API_KEY no está configurada"
- Verificar que la API Key esté en el archivo `.env`
- Reiniciar la aplicación después de agregar la variable

### Error: "No se encontró JSON válido"
- Problema temporal con la API de Gemini
- El sistema usa fallback automáticamente

### Baja precisión en categorización
- Revisar el prompt en `gemini.service.ts`
- Agregar más ejemplos específicos
- Ajustar palabras clave por categoría

## 🚀 Próximas Mejoras

- [ ] Cache de respuestas para mensajes similares
- [ ] Soporte para mensajes de audio (transcripción)
- [ ] Retry logic con backoff exponencial
- [ ] Dashboard web para métricas
- [ ] A/B testing de prompts
- [ ] Soporte multiidioma

## 📞 Soporte

Para problemas o mejoras, revisar:
1. Logs de la aplicación
2. Métricas en `/webhook/metrics`
3. Tests de integración
4. Documentación de Gemini AI 