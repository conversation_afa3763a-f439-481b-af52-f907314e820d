# Migración a Procesamiento de Lenguaje Natural con Gemini

## Objetivo
Cambiar el sistema actual de mensajes estructurados a un sistema que procese mensajes en lenguaje natural usando la API de Gemini para extraer información de gastos.

## Pre-requisitos
- [ ] API Key de Gemini configurada
- [ ] Proyecto NestJS funcionando
- [ ] WhatsApp API funcionando

## Fase 1: Configuración de Gemini

### 1.1 Instalación de Dependencias
- [X] Instalar el SDK de Google AI:
  ```bash
  npm install @google/generative-ai
  ```
- [X] Instalar tipos de Node.js si no están:
  ```bash
  npm install --save-dev @types/node
  ```

### 1.2 Configuración de Variables de Entorno
- [X] Agregar la API Key de Gemini al archivo `.env`:
  ```env
  GEMINI_API_KEY=tu_api_key_aqui
  ```
- [X] Agregar la variable al `ConfigModule` en `app.module.ts`

### 1.3 Creación del Servicio Gemini
- [X] Crear archivo `src/ai/gemini.service.ts`
- [X] Implementar clase `GeminiService` con método para procesar texto
- [X] Crear interfaz para respuesta estructurada de gastos
- [ ] Implementar método para convertir audio a texto (si es necesario)

## Fase 2: Diseño del Prompt y Estructura de Datos

### 2.1 Definir Estructura de Respuesta
- [X] Crear interfaz `ExpenseData` con campos:
  - `amount: number`
  - `category: string`
  - `description: string`
  - `confidence: number` (0-1)
  - `isValid: boolean`

### 2.2 Crear Prompt Sistema
- [X] Diseñar prompt que incluya:
  - Contexto sobre gastos argentinos
  - Categorías disponibles (Supermercado, Transporte, Comida, etc.)
  - Ejemplos de conversión
  - Formato de respuesta JSON
  - Manejo de jerga argentina

### 2.3 Categorías Predefinidas
- [X] Definir lista de categorías:
  - Supermercado/Almacén
  - Transporte
  - Comida/Restaurant
  - Entretenimiento
  - Salud
  - Servicios
  - Otros

## Fase 3: Implementación del Servicio

### 3.1 Archivo: `src/ai/gemini.service.ts`
- [X] Importar dependencias necesarias
- [X] Configurar cliente de Gemini
- [X] Implementar método `processNaturalLanguage(text: string)`
- [X] Implementar validación de respuesta
- [X] Manejar errores de API
- [X] Agregar logging detallado

### 3.2 Archivo: `src/ai/interfaces/expense-data.interface.ts`
- [X] Definir interfaz `ExpenseData`
- [X] Definir tipos para categorías
- [X] Exportar interfaces

### 3.3 Archivo: `src/ai/ai.module.ts`
- [X] Crear módulo AI
- [X] Importar ConfigModule
- [X] Exportar GeminiService

## Fase 4: Integración con WhatsApp

### 4.1 Modificar Webhook Service
- [X] Archivo: `src/webhook/webhook.service.ts`
- [X] Importar GeminiService
- [X] Modificar método `processWhatsAppMessage`
- [X] Agregar lógica para detectar tipo de mensaje
- [X] Implementar procesamiento de texto natural
- [X] Mantener compatibilidad con formato anterior

### 4.2 Manejar Diferentes Tipos de Mensaje
- [X] Detectar si es mensaje estructurado (formato anterior)
- [X] Detectar si es lenguaje natural
- [X] Procesar mensajes de audio (transcripción)
- [X] Implementar fallback en caso de error

### 4.3 Mejorar Respuestas al Usuario
- [X] Enviar confirmación con datos extraídos
- [X] Solicitar aclaración si la confianza es baja
- [X] Manejar casos donde no se puede extraer información

## Fase 5: Manejo de Audio

### 5.1 Transcripción de Audio (Opcional)
- [X] Investigar API de transcripción (puede usar Gemini o Speech-to-Text)
- [X] Implementar descarga de audio de WhatsApp
- [X] Convertir audio a texto
- [X] Procesar texto transcrito con Gemini

### 5.2 Integración Audio-Texto
- [X] Modificar webhook para manejar mensajes de audio
- [X] Implementar pipeline: Audio → Texto → Gemini → Gasto
- [X] Agregar validación de formato de audio

## Fase 6: Testing y Validación

### 6.1 Casos de Prueba
- [X] Crear casos de prueba para frases argentinas típicas:
  - "Compré en el chino por 2500"
  - "Gasté 1500 mangos en el bondi"
  - "Me salió 3400 el super de hoy"
  - "Fui al doctor, me cobró 8000"

### 6.2 Testing del Servicio
- [X] Crear tests unitarios para GeminiService
- [X] Crear tests de integración
- [X] Validar diferentes formatos de entrada
- [X] Probar casos edge (números en palabras, etc.)

### 6.3 Validación Manual
- [X] Probar con usuarios reales
- [ ] Ajustar prompt según feedback
- [ ] Optimizar categorización
- [ ] Mejorar confianza del modelo

## Fase 7: Optimización y Monitoreo

### 7.1 Logging y Métricas
- [X] Implementar logging detallado de interacciones
- [X] Monitorear accuracy del modelo
- [X] Trackear costos de API
- [X] Implementar métricas de performance

### 7.2 Cache y Optimización
- [ ] Implementar cache para respuestas similares
- [ ] Optimizar prompts para reducir tokens
- [ ] Implementar rate limiting
- [ ] Manejar timeouts de API

### 7.3 Fallbacks y Robustez
- [X] Implementar fallback al formato anterior
- [X] Manejar errores de API de Gemini
- [ ] Implementar retry logic
- [ ] Notificar errores críticos

## Estructura de Archivos a Crear/Modificar

```
src/
├── ai/
│   ├── ai.module.ts
│   ├── gemini.service.ts
│   └── interfaces/
│       └── expense-data.interface.ts
├── webhook/
│   ├── webhook.service.ts (modificar)
│   └── webhook.controller.ts (puede necesitar modificación)
└── expense/
    └── expense-business.service.ts (posibles modificaciones)
```

## Ejemplo de Prompt para Gemini

```
Eres un asistente que extrae información de gastos de mensajes en español argentino.

Categorías disponibles:
- Supermercado: compras en supermercados, almacenes, "chino"
- Transporte: colectivo, bondi, taxi, uber, nafta
- Comida: restaurant, delivery, café
- Entretenimiento: cine, bar, juegos
- Salud: médico, farmacia, odontólogo
- Servicios: luz, gas, internet, celular
- Otros: compras varias

Ejemplos:
"Compré en el chino por 2500" → {"amount": 2500, "category": "Supermercado", "description": "Compra en almacén"}
"Gasté 1500 mangos en el bondi" → {"amount": 1500, "category": "Transporte", "description": "Viaje en colectivo"}

Responde SOLO con JSON válido:
{"amount": number, "category": string, "description": string, "confidence": number, "isValid": boolean}
```

## Checklist Final de Implementación

- [X] Todas las dependencias instaladas
- [X] Servicio Gemini implementado y funcionando
- [X] Integración con webhook completada
- [X] Tests básicos pasando
- [X] Documentación actualizada
- [X] Variables de entorno configuradas
- [X] Sistema de logging implementado
- [X] Fallbacks configurados
- [X] Validación manual completada
- [ ] Deploy y monitoreo configurado

## Notas Importantes

- **Costos**: Monitorear el uso de la API de Gemini para controlar costos
- **Latencia**: La respuesta puede ser más lenta que el procesamiento actual
- **Accuracy**: Requerirá iteración y ajuste del prompt basado en uso real
- **Fallback**: Mantener la funcionalidad anterior como respaldo
- **Privacy**: Considerar qué datos se envían a la API de Gemini