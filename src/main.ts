import { NestFactory } from '@nestjs/core';
import { AppCleanModule } from './app-clean.module';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppLoggerService } from './common/services/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppCleanModule);

  // Get configuration service
  const configService = app.get(ConfigService);

  // Set up custom logger
  const logger = app.get(AppLoggerService);
  logger.setContext('Bootstrap');
  app.useLogger(logger);

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
  }));

  // Enable CORS if needed
  app.enableCors({
    origin: true,
    credentials: true,
  });

  const port = configService.get<number>('port', 3000);

  await app.listen(port);

  logger.info(`🚀 Application is running on port ${port}`, {
    operation: 'BOOTSTRAP',
    port,
    environment: configService.get<string>('nodeEnv'),
  });
}

bootstrap().catch((error) => {
  console.error('❌ Error starting application:', error);
  process.exit(1);
});
