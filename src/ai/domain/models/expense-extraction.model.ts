export enum ExpenseCategory {
  SUPERMERCADO = 'Supermercado',
  TRANSPORTE = 'Transporte',
  COMIDA = 'Comida',
  ENTRETENIMIENTO = 'Entretenimiento',
  SALUD = 'Salud',
  SERVICIOS = 'Servicios',
  OTROS = 'Otros',
}

export class ExpenseExtraction {
  private readonly _amount: number;
  private readonly _category: ExpenseCategory;
  private readonly _description: string;
  private readonly _confidence: number;
  private readonly _isValid: boolean;
  private readonly _transcription?: string;

  constructor(
    amount: number,
    category: ExpenseCategory,
    description: string,
    confidence: number,
    isValid: boolean,
    transcription?: string
  ) {
    if (amount < 0) {
      throw new Error('Amount cannot be negative');
    }
    if (confidence < 0 || confidence > 1) {
      throw new Error('Confidence must be between 0 and 1');
    }
    if (!description || description.trim().length === 0) {
      throw new Error('Description cannot be empty');
    }

    this._amount = amount;
    this._category = category;
    this._description = description.trim();
    this._confidence = confidence;
    this._isValid = isValid;
    this._transcription = transcription?.trim();
  }

  get amount(): number {
    return this._amount;
  }

  get category(): ExpenseCategory {
    return this._category;
  }

  get description(): string {
    return this._description;
  }

  get confidence(): number {
    return this._confidence;
  }

  get isValid(): boolean {
    return this._isValid;
  }

  get transcription(): string | undefined {
    return this._transcription;
  }

  get hasHighConfidence(): boolean {
    return this._confidence >= 0.8;
  }

  get hasLowConfidence(): boolean {
    return this._confidence < 0.6;
  }

  get isReliable(): boolean {
    return this._isValid && this._confidence >= 0.6;
  }

  toString(): string {
    const transcriptionInfo = this._transcription ? ` (from: "${this._transcription}")` : '';
    return `$${this._amount} - ${this._category} - ${this._description} (${(this._confidence * 100).toFixed(1)}% confidence)${transcriptionInfo}`;
  }

  toJSON(): {
    amount: number;
    category: string;
    description: string;
    confidence: number;
    isValid: boolean;
    transcription?: string;
  } {
    return {
      amount: this._amount,
      category: this._category,
      description: this._description,
      confidence: this._confidence,
      isValid: this._isValid,
      transcription: this._transcription,
    };
  }

  static fromJSON(data: {
    amount: number;
    category: string;
    description: string;
    confidence: number;
    isValid: boolean;
    transcription?: string;
  }): ExpenseExtraction {
    const category = Object.values(ExpenseCategory).find(cat => cat === data.category) || ExpenseCategory.OTROS;
    
    return new ExpenseExtraction(
      data.amount,
      category,
      data.description,
      data.confidence,
      data.isValid,
      data.transcription
    );
  }

  static createInvalid(reason: string, transcription?: string): ExpenseExtraction {
    return new ExpenseExtraction(
      0,
      ExpenseCategory.OTROS,
      reason,
      0,
      false,
      transcription
    );
  }
}
