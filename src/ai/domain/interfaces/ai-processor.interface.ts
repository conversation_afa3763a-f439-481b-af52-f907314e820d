export interface ProcessingResult {
  success: boolean;
  data?: ExpenseExtractionResult;
  error?: string;
  transcription?: string;
}

export interface ExpenseExtractionResult {
  amount: number;
  category: string;
  description: string;
  confidence: number;
  isValid: boolean;
  transcription?: string;
}

export interface AudioProcessingResult extends ProcessingResult {
  transcription?: string;
}

export interface IAiProcessor {
  /**
   * Process natural language text to extract expense information
   */
  processNaturalLanguage(text: string): Promise<ProcessingResult>;

  /**
   * Process audio message to extract expense information
   */
  processAudioMessage(audioBuffer: Buffer, mimeType: string): Promise<AudioProcessingResult>;

  /**
   * Get processing metrics and statistics
   */
  getMetrics(): ProcessingMetrics;
}

export interface ProcessingMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageConfidence: number;
  categoriesUsed: Record<string, number>;
  processingTimes: number[];
  lastUpdated: Date;
}
