import { Injectable } from '@nestjs/common';

export interface GeminiMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageConfidence: number;
  categoriesUsed: Record<string, number>;
  processingTimes: number[];
  lastUpdated: Date;
}

@Injectable()
export class LoggingService {
  private metrics: GeminiMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageConfidence: 0,
    categoriesUsed: {},
    processingTimes: [],
    lastUpdated: new Date()
  };

  logGeminiRequest(
    input: string, 
    result: any, 
    processingTime: number, 
    success: boolean
  ): void {
    console.log('=== GEMINI METRICS LOG ===');
    console.log('Input:', input);
    console.log('Success:', success);
    console.log('Processing time:', processingTime, 'ms');
    
    // Actualizar métricas
    this.metrics.totalRequests++;
    this.metrics.processingTimes.push(processingTime);
    
    // Mantener solo los últimos 100 tiempos para calcular promedio
    if (this.metrics.processingTimes.length > 100) {
      this.metrics.processingTimes = this.metrics.processingTimes.slice(-100);
    }
    
    if (success && result.data) {
      this.metrics.successfulRequests++;
      
      // Actualizar confianza promedio
      const totalConfidence = this.metrics.averageConfidence * (this.metrics.successfulRequests - 1) + result.data.confidence;
      this.metrics.averageConfidence = totalConfidence / this.metrics.successfulRequests;
      
      // Contar categorías
      const category = result.data.category;
      this.metrics.categoriesUsed[category] = (this.metrics.categoriesUsed[category] || 0) + 1;
      
      console.log('Confidence:', result.data.confidence);
      console.log('Category:', category);
    } else {
      this.metrics.failedRequests++;
      console.log('Error:', result.error);
    }
    
    this.metrics.lastUpdated = new Date();
    
    // Log de resumen cada 10 requests
    if (this.metrics.totalRequests % 10 === 0) {
      this.logSummary();
    }
  }

  getMetrics(): GeminiMetrics {
    return { ...this.metrics };
  }

  logSummary(): void {
    console.log('\n📊 === RESUMEN DE MÉTRICAS GEMINI ===');
    console.log(`Total requests: ${this.metrics.totalRequests}`);
    console.log(`Success rate: ${((this.metrics.successfulRequests / this.metrics.totalRequests) * 100).toFixed(1)}%`);
    console.log(`Average confidence: ${this.metrics.averageConfidence.toFixed(2)}`);
    
    const avgProcessingTime = this.metrics.processingTimes.length > 0 
      ? this.metrics.processingTimes.reduce((a, b) => a + b, 0) / this.metrics.processingTimes.length 
      : 0;
    console.log(`Average processing time: ${avgProcessingTime.toFixed(0)}ms`);
    
    console.log('Categories used:');
    Object.entries(this.metrics.categoriesUsed).forEach(([category, count]) => {
      console.log(`  ${category}: ${count}`);
    });
    console.log('================================\n');
  }

  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageConfidence: 0,
      categoriesUsed: {},
      processingTimes: [],
      lastUpdated: new Date()
    };
    console.log('📊 Métricas reseteadas');
  }

  logCostEstimate(): void {
    // Estimación básica de costos (depende del modelo y tokens)
    const estimatedTokensPerRequest = 150; // Promedio estimado
    const costPerMillionTokens = 7; // USD para Gemini Pro (aproximado)
    const estimatedCost = (this.metrics.totalRequests * estimatedTokensPerRequest * costPerMillionTokens) / 1000000;
    
    console.log(`💰 Costo estimado: $${estimatedCost.toFixed(4)} USD (${this.metrics.totalRequests} requests)`);
  }
} 