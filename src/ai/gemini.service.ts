import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { ExpenseData, ExpenseCategory, GeminiResponse } from './interfaces/expense-data.interface';
import { LoggingService } from './logging.service';

@Injectable()
export class GeminiService {
  private readonly genAI: GoogleGenerativeAI;
  private readonly model;

  constructor(
    private configService: ConfigService,
    private loggingService: LoggingService,
  ) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY no está configurada');
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
  }

  private getSystemPrompt(): string {
    return `Eres un asistente que extrae información de gastos de mensajes en español argentino.

Categorías disponibles y sus indicadores:
- Supermercado: compras en supermercados, almacenes, "chino", verdulería, carnicería, panadería, despensa
- Transporte: colectivo, bondi, taxi, uber, cabify, subte, tren, nafta, combustible, peaje
- Comida: restaurant, delivery, café, bar, pizzería, asado, almuerzo, cena, desayuno
- Entretenimiento: cine, teatro, juegos, concierto, recital, boliche, salida nocturna
- Salud: médico, doctor, farmacia, odontólogo, dentista, clínica, hospital, medicamentos
- Servicios: luz, gas, internet, celular, cable, streaming, peluquería, lavandería
- Otros: cualquier gasto que no encaje en las categorías anteriores

Jerga argentina común:
- "mangos", "lucas", "gambas" = pesos
- "chino" = almacén/supermercado pequeño
- "bondi" = colectivo/autobús
- "nafta" = combustible/gasolina
- "doc" = doctor/médico

Ejemplos de conversión:
"Compré en el chino por 2500" → {"amount": 2500, "category": "Supermercado", "description": "Compra en almacén", "confidence": 0.9, "isValid": true}
"Gasté 1500 mangos en el bondi" → {"amount": 1500, "category": "Transporte", "description": "Viaje en colectivo", "confidence": 0.85, "isValid": true}
"Me salió 3400 el super de hoy" → {"amount": 3400, "category": "Supermercado", "description": "Compra en supermercado", "confidence": 0.9, "isValid": true}
"Fui al doc, me cobró 8000" → {"amount": 8000, "category": "Salud", "description": "Consulta médica", "confidence": 0.8, "isValid": true}

Instrucciones:
1. Extrae el monto en números (convierte palabras como "mil", "dos mil" a números)
2. Identifica la categoría más apropiada basándote en las palabras clave
3. Crea una descripción breve y clara
4. Asigna un nivel de confianza (0-1) basado en qué tan claro es el mensaje
5. Marca isValid como false si no puedes extraer información útil

Responde ÚNICAMENTE con JSON válido, sin texto adicional:
{"amount": number, "category": "string", "description": "string", "confidence": number, "isValid": boolean}`;
  }

  async processNaturalLanguage(text: string): Promise<GeminiResponse> {
    const startTime = Date.now();
    
    try {
      console.log('=== PROCESANDO CON GEMINI ===');
      console.log('Texto recibido:', text);

      if (!text || text.trim().length === 0) {
        const result = {
          success: false,
          error: 'Texto vacío o inválido'
        };
        
        this.loggingService.logGeminiRequest(text, result, Date.now() - startTime, false);
        return result;
      }

      const prompt = `${this.getSystemPrompt()}

Mensaje a procesar: "${text}"`;

      console.log('Enviando prompt a Gemini...');
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const responseText = response.text();

      console.log('Respuesta de Gemini:', responseText);

      // Intentar parsear la respuesta JSON
      try {
        const jsonMatch = responseText.match(/\{.*\}/s);
        if (!jsonMatch) {
          throw new Error('No se encontró JSON válido en la respuesta');
        }

        const parsedData = JSON.parse(jsonMatch[0]);
        
        // Validar que tiene los campos requeridos
        if (!this.validateExpenseData(parsedData)) {
          throw new Error('Datos de gasto inválidos');
        }

        // Validar categoría
        if (!Object.values(ExpenseCategory).includes(parsedData.category)) {
          console.log('Categoría no válida, usando "Otros"');
          parsedData.category = ExpenseCategory.OTROS;
          parsedData.confidence = Math.max(0, parsedData.confidence - 0.2);
        }

        console.log('✅ Datos extraídos exitosamente:', parsedData);

        const finalResult = {
          success: true,
          data: parsedData as ExpenseData
        };

        // Log de métricas
        this.loggingService.logGeminiRequest(text, finalResult, Date.now() - startTime, true);

        return finalResult;

      } catch (parseError) {
        console.error('Error al parsear respuesta de Gemini:', parseError);
        const errorResult = {
          success: false,
          error: `Error al parsear respuesta: ${parseError.message}`
        };
        
        this.loggingService.logGeminiRequest(text, errorResult, Date.now() - startTime, false);
        return errorResult;
      }

    } catch (error) {
      console.error('❌ Error en Gemini Service:', error);
      const errorResult = {
        success: false,
        error: `Error de API: ${error.message}`
      };
      
      this.loggingService.logGeminiRequest(text, errorResult, Date.now() - startTime, false);
      return errorResult;
    }
  }

  private validateExpenseData(data: any): boolean {
    return (
      typeof data === 'object' &&
      typeof data.amount === 'number' &&
      data.amount > 0 &&
      typeof data.category === 'string' &&
      typeof data.description === 'string' &&
      typeof data.confidence === 'number' &&
      data.confidence >= 0 && data.confidence <= 1 &&
      typeof data.isValid === 'boolean'
    );
  }

  // Método para detectar si un mensaje es estructurado (formato anterior)
  isStructuredMessage(text: string): boolean {
    // Detecta patrones como "4000 pesos una coca cola" o "2500 transporte"
    const structuredPattern = /^\s*\d+(\.\d+)?\s+(pesos?\s+)?[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/i;
    return structuredPattern.test(text.trim());
  }

  // Método para obtener métricas
  getMetrics() {
    return this.loggingService.getMetrics();
  }

  // Método para mostrar resumen de métricas
  showMetrics() {
    this.loggingService.logSummary();
    this.loggingService.logCostEstimate();
  }

  async processAudioMessage(audioBuffer: Buffer, mimeType: string): Promise<GeminiResponse> {
    const startTime = Date.now();
    
    try {
      console.log('=== PROCESANDO AUDIO CON GEMINI ===');
      console.log('Tipo de audio:', mimeType);
      console.log('Tamaño del audio:', audioBuffer.length, 'bytes');

      if (!audioBuffer || audioBuffer.length === 0) {
        const result = {
          success: false,
          error: 'Audio vacío o inválido'
        };
        
        this.loggingService.logGeminiRequest('audio', result, Date.now() - startTime, false);
        return result;
      }

      // Crear el prompt para transcripción y extracción de gastos
      const prompt = `${this.getAudioSystemPrompt()}

Por favor, transcribe este audio y extrae la información de gastos si la hay.`;

      console.log('Enviando audio a Gemini para transcripción...');
      
      // Convertir buffer a base64 para Gemini
      const base64Audio = audioBuffer.toString('base64');
      
      const result = await this.model.generateContent([
        {
          inlineData: {
            data: base64Audio,
            mimeType: mimeType
          }
        },
        prompt
      ]);

      const response = await result.response;
      const responseText = response.text();

      console.log('Respuesta de Gemini (audio):', responseText);

      // Intentar parsear la respuesta JSON
      try {
        const jsonMatch = responseText.match(/\{.*\}/s);
        if (!jsonMatch) {
          throw new Error('No se encontró JSON válido en la respuesta');
        }

        const parsedData = JSON.parse(jsonMatch[0]);
        
        // Validar que tiene los campos requeridos
        if (!this.validateExpenseData(parsedData)) {
          throw new Error('Datos de gasto inválidos');
        }

        // Validar categoría
        if (!Object.values(ExpenseCategory).includes(parsedData.category)) {
          console.log('Categoría no válida, usando "Otros"');
          parsedData.category = ExpenseCategory.OTROS;
          parsedData.confidence = Math.max(0, parsedData.confidence - 0.2);
        }

        console.log('✅ Audio transcrito y datos extraídos exitosamente:', parsedData);

        const finalResult = {
          success: true,
          data: parsedData as ExpenseData,
          transcription: parsedData.transcription || 'Transcripción no disponible'
        };

        // Log de métricas
        this.loggingService.logGeminiRequest('audio', finalResult, Date.now() - startTime, true);

        return finalResult;

      } catch (parseError) {
        console.error('Error al parsear respuesta de Gemini (audio):', parseError);
        const errorResult = {
          success: false,
          error: `Error al parsear respuesta de audio: ${parseError.message}`
        };
        
        this.loggingService.logGeminiRequest('audio', errorResult, Date.now() - startTime, false);
        return errorResult;
      }

    } catch (error) {
      console.error('❌ Error en Gemini Service (audio):', error);
      const errorResult = {
        success: false,
        error: `Error de API de audio: ${error.message}`
      };
      
      this.loggingService.logGeminiRequest('audio', errorResult, Date.now() - startTime, false);
      return errorResult;
    }
  }

  private getAudioSystemPrompt(): string {
    return `Eres un asistente que transcribe audio y extrae información de gastos de mensajes en español argentino.

PASO 1: TRANSCRIPCIÓN
Primero, transcribe exactamente lo que se dice en el audio.

PASO 2: EXTRACCIÓN DE GASTOS
Si el audio contiene información sobre un gasto, extrae los datos siguientes:

Categorías disponibles y sus indicadores:
- Supermercado: compras en supermercados, almacenes, "chino", verdulería, carnicería, panadería, despensa
- Transporte: colectivo, bondi, taxi, uber, cabify, subte, tren, nafta, combustible, peaje
- Comida: restaurant, delivery, café, bar, pizzería, asado, almuerzo, cena, desayuno
- Entretenimiento: cine, teatro, juegos, concierto, recital, boliche, salida nocturna
- Salud: médico, doctor, farmacia, odontólogo, dentista, clínica, hospital, medicamentos
- Servicios: luz, gas, internet, celular, cable, streaming, peluquería, lavandería
- Otros: cualquier gasto que no encaje en las categorías anteriores

Jerga argentina común:
- "mangos", "lucas", "gambas" = pesos
- "chino" = almacén/supermercado pequeño
- "bondi" = colectivo/autobús
- "nafta" = combustible/gasolina
- "doc" = doctor/médico

Ejemplos de conversión:
Audio: "Compré en el chino por dos mil quinientos"
→ {"amount": 2500, "category": "Supermercado", "description": "Compra en almacén", "confidence": 0.9, "isValid": true, "transcription": "Compré en el chino por dos mil quinientos"}

Audio: "Gasté mil quinientos mangos en el bondi"
→ {"amount": 1500, "category": "Transporte", "description": "Viaje en colectivo", "confidence": 0.85, "isValid": true, "transcription": "Gasté mil quinientos mangos en el bondi"}

Instrucciones:
1. Transcribe el audio completo
2. Extrae el monto en números (convierte palabras como "mil", "dos mil" a números)
3. Identifica la categoría más apropiada basándote en las palabras clave
4. Crea una descripción breve y clara
5. Asigna un nivel de confianza (0-1) basado en qué tan claro es el mensaje
6. Marca isValid como false si no puedes extraer información útil de gastos
7. Incluye la transcripción completa

Responde ÚNICAMENTE con JSON válido, sin texto adicional:
{"amount": number, "category": "string", "description": "string", "confidence": number, "isValid": boolean, "transcription": "string"}`;
  }
} 