export enum ExpenseCategory {
  SUPERMERCADO = 'Supermercado',
  TRANSPORTE = 'Transporte',
  COMIDA = 'Comida',
  ENTRETENIMIENTO = 'Entretenimiento',
  SALUD = 'Salud',
  SERVICIOS = 'Servicios',
  OTROS = 'O<PERSON>s',
}

export interface ExpenseData {
  amount: number;
  category: string;
  description: string;
  confidence: number; // 0-1
  isValid: boolean;
  transcription?: string; // Para mensajes de audio
}

export interface GeminiResponse {
  success: boolean;
  data?: ExpenseData;
  error?: string;
  transcription?: string; // Para mensajes de audio
} 