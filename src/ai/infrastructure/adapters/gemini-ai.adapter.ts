import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { 
  IAiProcessor, 
  ProcessingResult, 
  AudioProcessingResult, 
  ProcessingMetrics,
  ExpenseExtractionResult 
} from '../../domain/interfaces/ai-processor.interface';

@Injectable()
export class GeminiAiA<PERSON>pter implements IAiProcessor {
  private readonly genAI: GoogleGenerativeAI;
  private readonly model;
  private metrics: ProcessingMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageConfidence: 0,
    categoriesUsed: {},
    processingTimes: [],
    lastUpdated: new Date()
  };

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY no está configurada');
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
  }

  async processNaturalLanguage(text: string): Promise<ProcessingResult> {
    const startTime = Date.now();
    
    try {
      console.log('=== PROCESSING TEXT WITH GEMINI ===');
      console.log('Input text:', text);

      const prompt = this.getTextSystemPrompt();
      const fullPrompt = `${prompt}\n\nTexto a analizar: "${text}"`;

      const result = await this.model.generateContent(fullPrompt);
      const response = await result.response;
      const responseText = response.text();

      console.log('Gemini response:', responseText);

      const expenseData = this.parseGeminiResponse(responseText);
      const processingTime = Date.now() - startTime;

      this.updateMetrics(expenseData, processingTime, true);

      return {
        success: true,
        data: expenseData
      };

    } catch (error) {
      console.error('Error processing with Gemini:', error);
      const processingTime = Date.now() - startTime;
      this.updateMetrics(null, processingTime, false);

      return {
        success: false,
        error: 'Error processing text with AI'
      };
    }
  }

  async processAudioMessage(audioBuffer: Buffer, mimeType: string): Promise<AudioProcessingResult> {
    const startTime = Date.now();
    
    try {
      console.log('=== PROCESSING AUDIO WITH GEMINI ===');
      console.log('Audio type:', mimeType);
      console.log('Audio size:', audioBuffer.length, 'bytes');

      const prompt = this.getAudioSystemPrompt();

      const audioPart = {
        inlineData: {
          data: audioBuffer.toString('base64'),
          mimeType: mimeType
        }
      };

      const result = await this.model.generateContent([prompt, audioPart]);
      const response = await result.response;
      const responseText = response.text();

      console.log('Gemini audio response:', responseText);

      const parsedResult = this.parseGeminiAudioResponse(responseText);
      const processingTime = Date.now() - startTime;

      this.updateMetrics(parsedResult.expenseData, processingTime, true);

      return {
        success: true,
        data: parsedResult.expenseData,
        transcription: parsedResult.transcription
      };

    } catch (error) {
      console.error('Error processing audio with Gemini:', error);
      const processingTime = Date.now() - startTime;
      this.updateMetrics(null, processingTime, false);

      return {
        success: false,
        error: 'Error processing audio with AI'
      };
    }
  }

  getMetrics(): ProcessingMetrics {
    return { ...this.metrics };
  }

  private getTextSystemPrompt(): string {
    return `Eres un asistente que extrae información de gastos de mensajes en español argentino.

Categorías disponibles y sus indicadores:
- Supermercado: compras en supermercados, almacenes, "chino", verdulería, carnicería, panadería, despensa
- Transporte: colectivo, bondi, taxi, uber, cabify, subte, tren, nafta, combustible, peaje
- Comida: restaurant, delivery, café, bar, pizzería, asado, almuerzo, cena, desayuno
- Entretenimiento: cine, teatro, juegos, concierto, recital, boliche, salida nocturna
- Salud: médico, doctor, farmacia, odontólogo, dentista, clínica, hospital, medicamentos
- Servicios: luz, gas, internet, celular, cable, streaming, peluquería, lavandería
- Otros: cualquier gasto que no encaje en las categorías anteriores

Responde SOLO con un JSON válido en este formato:
{
  "amount": número,
  "category": "categoría",
  "description": "descripción breve",
  "confidence": número entre 0 y 1,
  "isValid": true/false
}

Reglas:
- Si no puedes extraer información clara, usa "isValid": false
- La confianza debe reflejar qué tan seguro estás de la extracción
- La descripción debe ser concisa y clara
- El monto debe ser un número sin símbolos`;
  }

  private getAudioSystemPrompt(): string {
    return `Eres un asistente que transcribe audio y extrae información de gastos del español argentino.

Categorías disponibles y sus indicadores:
- Supermercado: compras en supermercados, almacenes, "chino", verdulería, carnicería, panadería, despensa
- Transporte: colectivo, bondi, taxi, uber, cabify, subte, tren, nafta, combustible, peaje
- Comida: restaurant, delivery, café, bar, pizzería, asado, almuerzo, cena, desayuno
- Entretenimiento: cine, teatro, juegos, concierto, recital, boliche, salida nocturna
- Salud: médico, doctor, farmacia, odontólogo, dentista, clínica, hospital, medicamentos
- Servicios: luz, gas, internet, celular, cable, streaming, peluquería, lavandería
- Otros: cualquier gasto que no encaje en las categorías anteriores

Responde SOLO con un JSON válido en este formato:
{
  "transcription": "texto transcrito exacto",
  "amount": número,
  "category": "categoría",
  "description": "descripción breve",
  "confidence": número entre 0 y 1,
  "isValid": true/false
}

Reglas:
- Transcribe exactamente lo que escuchas
- Si no puedes extraer información clara del gasto, usa "isValid": false
- La confianza debe considerar tanto la calidad del audio como la claridad del gasto
- La descripción debe ser concisa y clara
- El monto debe ser un número sin símbolos`;
  }

  private parseGeminiResponse(responseText: string): ExpenseExtractionResult {
    try {
      // Clean the response text
      const cleanedResponse = responseText.replace(/```json\n?|\n?```/g, '').trim();
      const parsed = JSON.parse(cleanedResponse);

      return {
        amount: parsed.amount || 0,
        category: parsed.category || 'Otros',
        description: parsed.description || 'Sin descripción',
        confidence: parsed.confidence || 0,
        isValid: parsed.isValid || false
      };
    } catch (error) {
      console.error('Error parsing Gemini response:', error);
      return {
        amount: 0,
        category: 'Otros',
        description: 'Error al procesar respuesta',
        confidence: 0,
        isValid: false
      };
    }
  }

  private parseGeminiAudioResponse(responseText: string): {
    expenseData: ExpenseExtractionResult;
    transcription?: string;
  } {
    try {
      const cleanedResponse = responseText.replace(/```json\n?|\n?```/g, '').trim();
      const parsed = JSON.parse(cleanedResponse);

      return {
        expenseData: {
          amount: parsed.amount || 0,
          category: parsed.category || 'Otros',
          description: parsed.description || 'Sin descripción',
          confidence: parsed.confidence || 0,
          isValid: parsed.isValid || false
        },
        transcription: parsed.transcription
      };
    } catch (error) {
      console.error('Error parsing Gemini audio response:', error);
      return {
        expenseData: {
          amount: 0,
          category: 'Otros',
          description: 'Error al procesar respuesta de audio',
          confidence: 0,
          isValid: false
        }
      };
    }
  }

  private updateMetrics(expenseData: ExpenseExtractionResult | null, processingTime: number, success: boolean): void {
    this.metrics.totalRequests++;
    this.metrics.processingTimes.push(processingTime);
    
    if (success) {
      this.metrics.successfulRequests++;
      
      if (expenseData) {
        // Update average confidence
        const totalConfidence = this.metrics.averageConfidence * (this.metrics.successfulRequests - 1) + expenseData.confidence;
        this.metrics.averageConfidence = totalConfidence / this.metrics.successfulRequests;
        
        // Update category usage
        if (!this.metrics.categoriesUsed[expenseData.category]) {
          this.metrics.categoriesUsed[expenseData.category] = 0;
        }
        this.metrics.categoriesUsed[expenseData.category]++;
      }
    } else {
      this.metrics.failedRequests++;
    }
    
    this.metrics.lastUpdated = new Date();
    
    // Keep only last 100 processing times
    if (this.metrics.processingTimes.length > 100) {
      this.metrics.processingTimes = this.metrics.processingTimes.slice(-100);
    }
  }
}
