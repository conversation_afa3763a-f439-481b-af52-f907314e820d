import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Domain
import { IAiProcessor } from './domain/interfaces/ai-processor.interface';

// Application
import { ProcessNaturalLanguageUseCase } from './application/use-cases/process-natural-language.use-case';
import { ProcessAudioMessageUseCase } from './application/use-cases/process-audio-message.use-case';

// Infrastructure
import { GeminiAiAdapter } from './infrastructure/adapters/gemini-ai.adapter';

// Keep the existing logging service for now
import { LoggingService } from './logging.service';

@Module({
  imports: [ConfigModule],
  providers: [
    // AI Processor implementation
    {
      provide: IAiProcessor,
      useClass: GeminiAiAdapter,
    },
    
    // Use cases
    ProcessNaturalLanguageUseCase,
    ProcessAudioMessageUseCase,
    
    // Legacy services (to be refactored later)
    LoggingService,
  ],
  exports: [
    IAiProcessor,
    ProcessNaturalLanguageUseCase,
    ProcessAudioMessageUseCase,
    LoggingService, // Export for backward compatibility
  ],
})
export class AiCleanModule {}
