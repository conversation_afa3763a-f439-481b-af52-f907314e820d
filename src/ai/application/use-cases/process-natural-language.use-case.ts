import { Injectable, Inject } from '@nestjs/common';
import { IAiProcessor, ProcessingResult } from '../../domain/interfaces/ai-processor.interface';

@Injectable()
export class ProcessNaturalLanguageUseCase {
  constructor(@Inject('IAiProcessor') private readonly aiProcessor: IAiProcessor) {}

  async execute(text: string): Promise<ProcessingResult> {
    if (!text || text.trim().length === 0) {
      return {
        success: false,
        error: 'Text cannot be empty'
      };
    }

    if (text.trim().length > 1000) {
      return {
        success: false,
        error: 'Text is too long (maximum 1000 characters)'
      };
    }

    try {
      const result = await this.aiProcessor.processNaturalLanguage(text.trim());
      
      // Additional business logic validation
      if (result.success && result.data) {
        // Validate minimum confidence threshold
        if (result.data.confidence < 0.3) {
          return {
            success: false,
            error: 'Confidence too low to process expense',
            data: result.data
          };
        }

        // Validate amount is reasonable
        if (result.data.amount > 1000000) {
          return {
            success: false,
            error: 'Amount seems too high, please verify',
            data: result.data
          };
        }
      }

      return result;
    } catch (error) {
      console.error('Error processing natural language:', error);
      return {
        success: false,
        error: 'Failed to process text'
      };
    }
  }
}
