import { Injectable } from '@nestjs/common';
import { IAiProcessor, AudioProcessingResult } from '../../domain/interfaces/ai-processor.interface';

@Injectable()
export class ProcessAudioMessageUseCase {
  constructor(private readonly aiProcessor: IAiProcessor) {}

  async execute(audioBuffer: Buffer, mimeType: string): Promise<AudioProcessingResult> {
    if (!audioBuffer || audioBuffer.length === 0) {
      return {
        success: false,
        error: 'Audio buffer cannot be empty'
      };
    }

    if (!mimeType || !this.isSupportedAudioType(mimeType)) {
      return {
        success: false,
        error: 'Unsupported audio format'
      };
    }

    // Check audio size (max 10MB)
    if (audioBuffer.length > 10 * 1024 * 1024) {
      return {
        success: false,
        error: 'Audio file too large (maximum 10MB)'
      };
    }

    try {
      const result = await this.aiProcessor.processAudioMessage(audioBuffer, mimeType);
      
      // Additional business logic validation
      if (result.success && result.data) {
        // Validate minimum confidence threshold for audio
        if (result.data.confidence < 0.4) {
          return {
            success: false,
            error: 'Audio quality too low or unclear speech',
            data: result.data,
            transcription: result.transcription
          };
        }

        // Validate amount is reasonable
        if (result.data.amount > 1000000) {
          return {
            success: false,
            error: 'Amount seems too high, please verify',
            data: result.data,
            transcription: result.transcription
          };
        }
      }

      return result;
    } catch (error) {
      console.error('Error processing audio message:', error);
      return {
        success: false,
        error: 'Failed to process audio'
      };
    }
  }

  private isSupportedAudioType(mimeType: string): boolean {
    const supportedTypes = [
      'audio/ogg',
      'audio/mpeg',
      'audio/mp3',
      'audio/wav',
      'audio/webm',
      'audio/m4a',
      'audio/aac'
    ];
    
    return supportedTypes.includes(mimeType.toLowerCase());
  }
}
