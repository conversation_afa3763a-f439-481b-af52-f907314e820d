import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from '@upstash/redis';
import { ExpenseRepository, ExpenseFilters } from '../../domain/repositories/expense.repository';
import { Expense } from '../../domain/entities/expense.entity';
import { PhoneNumber } from '../../domain/value-objects/phone-number.value-object';
import { Category } from '../../domain/value-objects/category.value-object';

@Injectable()
export class RedisExpenseRepository implements ExpenseRepository {
  private readonly redis: Redis;

  constructor(private configService: ConfigService) {
    this.redis = new Redis({
      url: this.configService.get<string>('UPSTASH_REDIS_URL'),
      token: this.configService.get<string>('UPSTASH_REDIS_TOKEN'),
    });
  }

  async save(expense: Expense): Promise<void> {
    console.log('=== SAVING EXPENSE TO REDIS ===');
    console.log('Expense:', expense.toString());

    // Save individual expense
    const expenseKey = `expense:${expense.phoneNumber.value}:${expense.id}`;
    await this.redis.set(expenseKey, expense.toJSON());

    // Save to monthly index for quick queries
    const monthKey = `expenses:${expense.year}:${expense.month}`;
    const monthlyExpenses = await this.redis.get<any[]>(monthKey) || [];
    
    // Check if expense already exists in monthly index
    const existingIndex = monthlyExpenses.findIndex(e => e.id === expense.id);
    if (existingIndex >= 0) {
      monthlyExpenses[existingIndex] = expense.toJSON();
    } else {
      monthlyExpenses.push(expense.toJSON());
    }
    
    await this.redis.set(monthKey, monthlyExpenses);

    console.log(`Expense saved with key: ${expenseKey}`);
    console.log(`Added to monthly index: ${monthKey}`);
  }

  async findById(id: string): Promise<Expense | null> {
    // We need to search across all possible phone numbers since we don't know the phone number
    // This is not optimal, but Redis doesn't support complex queries
    // In a real scenario, we might want to maintain a separate index
    const keys = await this.redis.keys('expense:*');
    
    for (const key of keys) {
      const expenseData = await this.redis.get<any>(key);
      if (expenseData && expenseData.id === id) {
        return Expense.fromJSON(expenseData);
      }
    }
    
    return null;
  }

  async findByFilters(filters: ExpenseFilters): Promise<Expense[]> {
    let expenses: Expense[] = [];

    if (filters.year && filters.month) {
      // Use monthly index for efficiency
      expenses = await this.findByYearAndMonth(filters.year, filters.month);
    } else {
      // Fallback to scanning all expenses
      expenses = await this.getAllExpenses();
    }

    // Apply additional filters
    return expenses.filter(expense => {
      if (filters.category && !expense.isInCategory(filters.category)) {
        return false;
      }
      if (filters.phoneNumber && !expense.isFromPhoneNumber(filters.phoneNumber)) {
        return false;
      }
      if (filters.startDate && filters.endDate && !expense.isInDateRange(filters.startDate, filters.endDate)) {
        return false;
      }
      if (filters.year && expense.year !== filters.year) {
        return false;
      }
      if (filters.month && expense.month !== filters.month) {
        return false;
      }
      return true;
    });
  }

  async findByYearAndMonth(year: number, month: number): Promise<Expense[]> {
    console.log(`=== FINDING EXPENSES FOR ${year}/${month} ===`);
    
    const monthKey = `expenses:${year}:${month}`;
    const expensesData = await this.redis.get<any[]>(monthKey) || [];
    
    console.log(`Found ${expensesData.length} expenses in Redis`);
    
    return expensesData.map(data => Expense.fromJSON(data));
  }

  async findByPhoneNumber(phoneNumber: PhoneNumber): Promise<Expense[]> {
    const pattern = `expense:${phoneNumber.value}:*`;
    const keys = await this.redis.keys(pattern);
    
    const expenses: Expense[] = [];
    for (const key of keys) {
      const expenseData = await this.redis.get<any>(key);
      if (expenseData) {
        expenses.push(Expense.fromJSON(expenseData));
      }
    }
    
    return expenses.sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  async findByCategory(category: Category): Promise<Expense[]> {
    const allExpenses = await this.getAllExpenses();
    return allExpenses.filter(expense => expense.isInCategory(category));
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Expense[]> {
    const allExpenses = await this.getAllExpenses();
    return allExpenses.filter(expense => expense.isInDateRange(startDate, endDate));
  }

  async findByInstallmentGroup(groupId: string): Promise<Expense[]> {
    const allExpenses = await this.getAllExpenses();
    const groupExpenses = allExpenses.filter(expense => 
      expense.installmentDetails?.groupId === groupId
    );
    
    return groupExpenses.sort((a, b) => {
      if (a.installmentDetails && b.installmentDetails) {
        return a.installmentDetails.currentInstallment - b.installmentDetails.currentInstallment;
      }
      return 0;
    });
  }

  async findPendingInstallments(year: number, month: number): Promise<Expense[]> {
    const allExpenses = await this.getAllExpenses();
    
    return allExpenses.filter(expense => {
      if (!expense.isInstallment) return false;
      
      const installmentDetails = expense.installmentDetails!;
      if (installmentDetails.isLastInstallment) return false;
      
      // Check if this installment is due in the specified month/year
      const expenseDate = expense.date;
      const nextInstallmentDate = new Date(expenseDate);
      nextInstallmentDate.setMonth(nextInstallmentDate.getMonth() + 1);
      
      return nextInstallmentDate.getFullYear() === year && 
             nextInstallmentDate.getMonth() + 1 === month;
    });
  }

  async getAvailableYears(): Promise<number[]> {
    const allExpenses = await this.getAllExpenses();
    const years = [...new Set(allExpenses.map(expense => expense.year))];
    return years.sort((a, b) => b - a);
  }

  async getUsedCategories(): Promise<Category[]> {
    const allExpenses = await this.getAllExpenses();
    const categories = [...new Set(allExpenses.map(expense => expense.category.value))];
    return categories.map(cat => Category.fromString(cat));
  }

  async delete(id: string): Promise<void> {
    const expense = await this.findById(id);
    if (!expense) return;

    // Delete from individual key
    const expenseKey = `expense:${expense.phoneNumber.value}:${expense.id}`;
    await this.redis.del(expenseKey);

    // Remove from monthly index
    const monthKey = `expenses:${expense.year}:${expense.month}`;
    const monthlyExpenses = await this.redis.get<any[]>(monthKey) || [];
    const filteredExpenses = monthlyExpenses.filter(e => e.id !== id);
    await this.redis.set(monthKey, filteredExpenses);
  }

  async exists(id: string): Promise<boolean> {
    const expense = await this.findById(id);
    return expense !== null;
  }

  async countByFilters(filters: ExpenseFilters): Promise<number> {
    const expenses = await this.findByFilters(filters);
    return expenses.length;
  }

  async getTotalAmountByFilters(filters: ExpenseFilters): Promise<number> {
    const expenses = await this.findByFilters(filters);
    return expenses.reduce((total, expense) => total + expense.amount.amount, 0);
  }

  private async getAllExpenses(): Promise<Expense[]> {
    const keys = await this.redis.keys('expense:*');
    const expenses: Expense[] = [];
    
    for (const key of keys) {
      const expenseData = await this.redis.get<any>(key);
      if (expenseData) {
        expenses.push(Expense.fromJSON(expenseData));
      }
    }
    
    return expenses.sort((a, b) => b.date.getTime() - a.date.getTime());
  }
}
