import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Expense } from '../../../domain/entities/expense.entity';

export interface AudioData {
  buffer: Buffer;
  mimeType: string;
}

export interface ConvertedAudio {
  buffer: Buffer;
  mimeType: string;
}

@Injectable()
export class WhatsAppService {
  private readonly numberId: string;
  private readonly token: string;
  private pendingExpenses: Map<string, Expense> = new Map();

  constructor(private configService: ConfigService) {
    this.numberId = this.configService.get<string>('WHATSAPP_NUMBER_ID') || '';
    this.token = this.configService.get<string>('WHATSAPP_TOKEN') || '';
  }

  async sendMessage(phoneNumber: string, message: string): Promise<void> {
    try {
      console.log(`📱 [WHATSAPP] Sending message to ${phoneNumber}: ${message}`);

      // En modo desarrollo, solo simular el envío
      if (this.configService.get('NODE_ENV') === 'development' && (!this.token || this.token === '')) {
        console.log('🧪 [DEV MODE] Message simulated (WhatsApp API not configured)');
        return;
      }

      const url = `https://graph.facebook.com/v17.0/${this.numberId}/messages`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messaging_product: 'whatsapp',
          to: phoneNumber,
          type: 'text',
          text: {
            body: message,
          },
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error sending WhatsApp message:', errorText);
        throw new Error(`WhatsApp API error: ${response.status}`);
      }

      console.log('✅ Message sent successfully');
    } catch (error) {
      console.error('❌ Error sending WhatsApp message:', error);

      // En desarrollo, no fallar por errores de WhatsApp
      if (this.configService.get('NODE_ENV') === 'development') {
        console.log('🧪 [DEV MODE] Continuing despite WhatsApp error...');
        return;
      }

      throw error;
    }
  }

  async sendConfirmationButtons(expense: Expense): Promise<void> {
    const pendingId = this.generatePendingId();
    this.pendingExpenses.set(pendingId, expense);

    // Primero enviar el mensaje de "Confirmar gasto"
    await this.sendMessage(expense.phoneNumber.value, 'Confirmar gasto');

    // Luego enviar los botones interactivos
    const message = {
      messaging_product: 'whatsapp',
      to: expense.phoneNumber.value,
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: `¿Confirmas este gasto?\n\n` +
                `Monto: $${expense.amount.amount}\n` +
                `Categoría: ${expense.category.value}\n` +
                `Descripción: ${expense.description}`,
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: `confirm_${pendingId}`,
                title: 'Confirmar',
              },
            },
            {
              type: 'reply',
              reply: {
                id: `cancel_${pendingId}`,
                title: 'Cancelar',
              },
            },
          ],
        },
      },
    };

    await this.sendInteractiveMessage(expense.phoneNumber.value, message);
  }

  async downloadAudio(audioId: string): Promise<AudioData | null> {
    try {
      console.log(`Downloading audio with ID: ${audioId}`);

      // First, get the audio URL
      const mediaUrl = `https://graph.facebook.com/v17.0/${audioId}`;
      const mediaResponse = await fetch(mediaUrl, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
        },
      });

      if (!mediaResponse.ok) {
        console.error('Error getting audio URL:', await mediaResponse.text());
        return null;
      }

      const mediaData = await mediaResponse.json();
      const audioUrl = mediaData.url;
      const mimeType = mediaData.mime_type || 'audio/ogg';

      console.log('Audio URL:', audioUrl);
      console.log('MIME Type:', mimeType);

      // Download the actual audio file
      const audioResponse = await fetch(audioUrl, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
        },
      });

      if (!audioResponse.ok) {
        console.error('Error downloading audio file:', await audioResponse.text());
        return null;
      }

      const audioBuffer = Buffer.from(await audioResponse.arrayBuffer());
      console.log(`Audio downloaded successfully. Size: ${audioBuffer.length} bytes`);

      return {
        buffer: audioBuffer,
        mimeType: mimeType,
      };
    } catch (error) {
      console.error('Error downloading audio:', error);
      return null;
    }
  }

  async convertAudioForGemini(audioBuffer: Buffer, mimeType: string): Promise<ConvertedAudio> {
    // For now, we'll pass through the audio as-is
    // In a production environment, you might want to convert to a format that Gemini prefers
    console.log(`Converting audio from ${mimeType} for Gemini processing`);
    
    return {
      buffer: audioBuffer,
      mimeType: mimeType,
    };
  }

  getPendingExpense(pendingId: string): Expense | undefined {
    return this.pendingExpenses.get(pendingId);
  }

  removePendingExpense(pendingId: string): void {
    this.pendingExpenses.delete(pendingId);
  }

  private async sendInteractiveMessage(phoneNumber: string, message: any): Promise<void> {
    try {
      console.log(`📱 [WHATSAPP] Sending interactive message to ${phoneNumber}`);
      console.log('🔘 Buttons:', message.interactive.action.buttons.map(b => b.reply.title).join(', '));

      // En modo desarrollo, solo simular el envío
      if (this.configService.get('NODE_ENV') === 'development' && (!this.token || this.token === '')) {
        console.log('🧪 [DEV MODE] Interactive message simulated (WhatsApp API not configured)');
        return;
      }

      const url = `https://graph.facebook.com/v17.0/${this.numberId}/messages`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error sending interactive message:', errorText);
        throw new Error(`WhatsApp API error: ${response.status}`);
      }

      console.log('✅ Interactive message sent successfully');
    } catch (error) {
      console.error('❌ Error sending interactive message:', error);

      // En desarrollo, no fallar por errores de WhatsApp
      if (this.configService.get('NODE_ENV') === 'development') {
        console.log('🧪 [DEV MODE] Continuing despite WhatsApp error...');
        return;
      }

      throw error;
    }
  }

  private generatePendingId(): string {
    return `pending_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
  }
}
