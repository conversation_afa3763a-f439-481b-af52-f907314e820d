import { Module } from '@nestjs/common';
import { ExpenseController } from './expense.controller';
import { ExpenseService } from './expense.service';
import { ExpenseBusinessService } from './expense-business.service';
import { WhatsAppService } from './whatsapp.service';

@Module({
  controllers: [ExpenseController],
  providers: [ExpenseService, ExpenseBusinessService, WhatsAppService],
  exports: [ExpenseService, ExpenseBusinessService, WhatsAppService],
})
export class ExpenseModule {} 