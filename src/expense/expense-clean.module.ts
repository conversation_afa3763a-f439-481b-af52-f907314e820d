import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Domain (using string tokens for interfaces)
const EXPENSE_REPOSITORY = 'ExpenseRepository';

// Application
import { CreateExpenseUseCase } from './application/use-cases/create-expense.use-case';
import { GetExpensesUseCase } from './application/use-cases/get-expenses.use-case';
import { GetAvailableYearsUseCase } from './application/use-cases/get-available-years.use-case';
import { GetCategoriesUseCase } from './application/use-cases/get-categories.use-case';

// Infrastructure
import { RedisExpenseRepository } from './infrastructure/persistence/redis-expense.repository';
import { WhatsAppService } from './infrastructure/external/whatsapp/whatsapp.service';

// Presentation
import { ExpenseController } from './presentation/controllers/expense.controller';

@Module({
  imports: [ConfigModule],
  controllers: [ExpenseController],
  providers: [
    // Repository implementation
    {
      provide: EXPENSE_REPOSITORY,
      useClass: RedisExpenseRepository,
    },

    // Use cases
    CreateExpenseUseCase,
    GetExpensesUseCase,
    GetAvailableYearsUseCase,
    GetCategoriesUseCase,

    // External services
    WhatsAppService,
  ],
  exports: [
    EXPENSE_REPOSITORY,
    CreateExpenseUseCase,
    GetExpensesUseCase,
    GetAvailableYearsUseCase,
    GetCategoriesUseCase,
    WhatsAppService,
  ],
})
export class ExpenseCleanModule {}
