import { Controller, Get, Query, Param } from '@nestjs/common';
import { ExpenseService } from './expense.service';
import { ExpenseFilterDto } from './dto/expense-filter.dto';

@Controller('expenses')
export class ExpenseController {
  constructor(private readonly expenseService: ExpenseService) {}

  @Get()
  async getExpenses(@Query() filterDto: ExpenseFilterDto) {
    const { year, month, category, phoneNumber } = filterDto;
    return this.expenseService.getByYearAndMonth(year, month, category, phoneNumber);
  }

  @Get('pending-installments')
  async getPendingInstallments(@Query() filterDto: ExpenseFilterDto) {
    const { year, month } = filterDto;
    return this.expenseService.getPendingInstallments(year, month);
  }

  @Get('installment-group/:groupId')
  async getInstallmentGroup(@Param('groupId') groupId: string) {
    return this.expenseService.getInstallmentGroup(groupId);
  }

  @Get('years')
  async getAvailableYears() {
    return this.expenseService.getAvailableYears();
  }

  @Get('categories')
  async getCategories() {
    return this.expenseService.getCategories();
  }
} 