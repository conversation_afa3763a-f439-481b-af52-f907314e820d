import { IsN<PERSON>ber, IsOptional, IsString, IsDateString, Min, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class ExpenseFilterDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(2000)
  @Max(2100)
  year?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(12)
  month?: number;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;
}
