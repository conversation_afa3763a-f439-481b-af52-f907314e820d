import { IsN<PERSON>ber, IsOptional, IsString, IsDateString, Min, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class ExpenseFilterDto {
  @ApiPropertyOptional({
    description: 'Year to filter expenses',
    example: 2024,
    minimum: 2000,
    maximum: 2100
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(2000)
  @Max(2100)
  year?: number;

  @ApiPropertyOptional({
    description: 'Month to filter expenses (1-12)',
    example: 3,
    minimum: 1,
    maximum: 12
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(12)
  month?: number;

  @ApiPropertyOptional({
    description: 'Category to filter expenses',
    example: 'Alimentos',
    enum: ['Alimentos', 'Transporte', 'Servicios', 'Entretenimiento', 'Salud', 'Educación', 'Hogar', 'Otros', 'all']
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({
    description: 'Phone number to filter expenses',
    example: '5491123456789'
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Start date for date range filter (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for date range filter (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}
