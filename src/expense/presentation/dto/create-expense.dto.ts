import { <PERSON>N<PERSON>ber, IsString, <PERSON><PERSON><PERSON><PERSON>, IsPos<PERSON>, <PERSON><PERSON>ength, <PERSON><PERSON>ength } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateInstallmentDetailsDto {
  @IsString()
  @MinLength(1)
  group_id: string;

  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  current_installment: number;

  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  total_installments: number;
}

export class CreateExpenseDto {
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  amount: number;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  category: string;

  @IsString()
  @MinLength(1)
  @MaxLength(500)
  description: string;

  @IsString()
  @MinLength(1)
  phoneNumber: string;

  @IsOptional()
  installment_details?: CreateInstallmentDetailsDto;
}
