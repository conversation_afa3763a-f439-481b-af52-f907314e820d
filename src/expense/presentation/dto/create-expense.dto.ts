import { IsN<PERSON>ber, IsString, IsOptional, IsPos<PERSON>, MaxLength, MinLength } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateInstallmentDetailsDto {
  @ApiProperty({
    description: 'Unique identifier for the installment group',
    example: 'installment_1234567890_abc123'
  })
  @IsString()
  @MinLength(1)
  group_id: string;

  @ApiProperty({
    description: 'Current installment number',
    example: 1,
    minimum: 1
  })
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  current_installment: number;

  @ApiProperty({
    description: 'Total number of installments',
    example: 12,
    minimum: 1
  })
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  total_installments: number;
}

export class CreateExpenseDto {
  @ApiProperty({
    description: 'Amount of the expense in pesos',
    example: 2500.50,
    minimum: 0.01
  })
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  amount: number;

  @ApiProperty({
    description: 'Category of the expense',
    example: 'Alimentos',
    enum: ['Alimentos', 'Transporte', 'Servicios', 'Entretenimiento', 'Salud', 'Educación', 'Hogar', 'Otros']
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  category: string;

  @ApiProperty({
    description: 'Description of the expense',
    example: 'Compra en el supermercado',
    maxLength: 500
  })
  @IsString()
  @MinLength(1)
  @MaxLength(500)
  description: string;

  @ApiProperty({
    description: 'Phone number of the user',
    example: '5491123456789'
  })
  @IsString()
  @MinLength(1)
  phoneNumber: string;

  @ApiPropertyOptional({
    description: 'Installment details if this is an installment payment',
    type: CreateInstallmentDetailsDto
  })
  @IsOptional()
  installment_details?: CreateInstallmentDetailsDto;
}
