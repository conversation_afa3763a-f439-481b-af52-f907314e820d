import { Controller, Get, Post, Query, Param, Body, HttpStatus, HttpException } from '@nestjs/common';
import { CreateExpenseUseCase } from '../../application/use-cases/create-expense.use-case';
import { GetExpensesUseCase } from '../../application/use-cases/get-expenses.use-case';
import { GetAvailableYearsUseCase } from '../../application/use-cases/get-available-years.use-case';
import { GetCategoriesUseCase } from '../../application/use-cases/get-categories.use-case';
import { CreateExpenseDto } from '../dto/create-expense.dto';
import { ExpenseFilterDto } from '../dto/expense-filter.dto';
import { ExpenseListResponseDto, ExpenseResponseDto } from '../../application/dto/expense-response.dto';

@Controller('expenses')
export class ExpenseController {
  constructor(
    private readonly createExpenseUseCase: CreateExpenseUseCase,
    private readonly getExpensesUseCase: GetExpensesUseCase,
    private readonly getAvailableYearsUseCase: GetAvailableYearsUseCase,
    private readonly getCategoriesUseCase: GetCategoriesUseCase,
  ) {}

  @Post()
  async createExpense(@Body() createExpenseDto: CreateExpenseDto): Promise<ExpenseResponseDto> {
    try {
      return await this.createExpenseUseCase.execute(createExpenseDto);
    } catch (error) {
      console.error('Error creating expense:', error);
      throw new HttpException(
        error.message || 'Failed to create expense',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get()
  async getExpenses(@Query() filterDto: ExpenseFilterDto): Promise<ExpenseListResponseDto> {
    try {
      return await this.getExpensesUseCase.execute(filterDto);
    } catch (error) {
      console.error('Error getting expenses:', error);
      throw new HttpException(
        error.message || 'Failed to get expenses',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('pending-installments')
  async getPendingInstallments(@Query() filterDto: ExpenseFilterDto): Promise<ExpenseListResponseDto> {
    try {
      if (!filterDto.year || !filterDto.month) {
        throw new HttpException(
          'Year and month are required for pending installments',
          HttpStatus.BAD_REQUEST
        );
      }
      return await this.getExpensesUseCase.getPendingInstallments(filterDto.year, filterDto.month);
    } catch (error) {
      console.error('Error getting pending installments:', error);
      throw new HttpException(
        error.message || 'Failed to get pending installments',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('installment-group/:groupId')
  async getInstallmentGroup(@Param('groupId') groupId: string): Promise<ExpenseListResponseDto> {
    try {
      return await this.getExpensesUseCase.getInstallmentGroup(groupId);
    } catch (error) {
      console.error('Error getting installment group:', error);
      throw new HttpException(
        error.message || 'Failed to get installment group',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('years')
  async getAvailableYears(): Promise<number[]> {
    try {
      return await this.getAvailableYearsUseCase.execute();
    } catch (error) {
      console.error('Error getting available years:', error);
      throw new HttpException(
        error.message || 'Failed to get available years',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories')
  async getCategories(): Promise<string[]> {
    try {
      return await this.getCategoriesUseCase.execute();
    } catch (error) {
      console.error('Error getting categories:', error);
      throw new HttpException(
        error.message || 'Failed to get categories',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories/used')
  async getUsedCategories(): Promise<string[]> {
    try {
      return await this.getCategoriesUseCase.getUsedCategories();
    } catch (error) {
      console.error('Error getting used categories:', error);
      throw new HttpException(
        error.message || 'Failed to get used categories',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories/available')
  getAvailableCategories(): string[] {
    try {
      return this.getCategoriesUseCase.getAvailableCategories();
    } catch (error) {
      console.error('Error getting available categories:', error);
      throw new HttpException(
        error.message || 'Failed to get available categories',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
