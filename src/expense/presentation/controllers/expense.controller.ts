import { Controller, Get, Post, Query, Param, Body, HttpStatus, HttpException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { CreateExpenseUseCase } from '../../application/use-cases/create-expense.use-case';
import { GetExpensesUseCase } from '../../application/use-cases/get-expenses.use-case';
import { GetAvailableYearsUseCase } from '../../application/use-cases/get-available-years.use-case';
import { GetCategoriesUseCase } from '../../application/use-cases/get-categories.use-case';
import { CreateExpenseDto } from '../dto/create-expense.dto';
import { ExpenseFilterDto } from '../dto/expense-filter.dto';
import { ExpenseListResponseDto, ExpenseResponseDto } from '../../application/dto/expense-response.dto';

@ApiTags('expenses')
@Controller('expenses')
export class ExpenseController {
  constructor(
    private readonly createExpenseUseCase: CreateExpenseUseCase,
    private readonly getExpensesUseCase: GetExpensesUseCase,
    private readonly getAvailableYearsUseCase: GetAvailableYearsUseCase,
    private readonly getCategoriesUseCase: GetCategoriesUseCase,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new expense' })
  @ApiResponse({ 
    status: 201, 
    description: 'Expense created successfully',
    type: ExpenseResponseDto
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data' 
  })
  async createExpense(@Body() createExpenseDto: CreateExpenseDto): Promise<ExpenseResponseDto> {
    try {
      return await this.createExpenseUseCase.execute(createExpenseDto);
    } catch (error) {
      console.error('Error creating expense:', error);
      throw new HttpException(
        error.message || 'Failed to create expense',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get expenses with optional filters' })
  @ApiResponse({ 
    status: 200, 
    description: 'Expenses retrieved successfully',
    type: ExpenseListResponseDto
  })
  async getExpenses(@Query() filterDto: ExpenseFilterDto): Promise<ExpenseListResponseDto> {
    try {
      return await this.getExpensesUseCase.execute(filterDto);
    } catch (error) {
      console.error('Error getting expenses:', error);
      throw new HttpException(
        error.message || 'Failed to get expenses',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('pending-installments')
  @ApiOperation({ summary: 'Get pending installments for a specific month' })
  @ApiQuery({ name: 'year', required: true, type: Number })
  @ApiQuery({ name: 'month', required: true, type: Number })
  @ApiResponse({ 
    status: 200, 
    description: 'Pending installments retrieved successfully',
    type: ExpenseListResponseDto
  })
  async getPendingInstallments(@Query() filterDto: ExpenseFilterDto): Promise<ExpenseListResponseDto> {
    try {
      if (!filterDto.year || !filterDto.month) {
        throw new HttpException(
          'Year and month are required for pending installments',
          HttpStatus.BAD_REQUEST
        );
      }
      return await this.getExpensesUseCase.getPendingInstallments(filterDto.year, filterDto.month);
    } catch (error) {
      console.error('Error getting pending installments:', error);
      throw new HttpException(
        error.message || 'Failed to get pending installments',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('installment-group/:groupId')
  @ApiOperation({ summary: 'Get all expenses in an installment group' })
  @ApiParam({ name: 'groupId', description: 'Installment group ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Installment group expenses retrieved successfully',
    type: ExpenseListResponseDto
  })
  async getInstallmentGroup(@Param('groupId') groupId: string): Promise<ExpenseListResponseDto> {
    try {
      return await this.getExpensesUseCase.getInstallmentGroup(groupId);
    } catch (error) {
      console.error('Error getting installment group:', error);
      throw new HttpException(
        error.message || 'Failed to get installment group',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('years')
  @ApiOperation({ summary: 'Get all available years that have expenses' })
  @ApiResponse({ 
    status: 200, 
    description: 'Available years retrieved successfully',
    type: [Number]
  })
  async getAvailableYears(): Promise<number[]> {
    try {
      return await this.getAvailableYearsUseCase.execute();
    } catch (error) {
      console.error('Error getting available years:', error);
      throw new HttpException(
        error.message || 'Failed to get available years',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get all available expense categories' })
  @ApiResponse({ 
    status: 200, 
    description: 'Categories retrieved successfully',
    type: [String]
  })
  async getCategories(): Promise<string[]> {
    try {
      return await this.getCategoriesUseCase.execute();
    } catch (error) {
      console.error('Error getting categories:', error);
      throw new HttpException(
        error.message || 'Failed to get categories',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories/used')
  @ApiOperation({ summary: 'Get categories that have been used in expenses' })
  @ApiResponse({ 
    status: 200, 
    description: 'Used categories retrieved successfully',
    type: [String]
  })
  async getUsedCategories(): Promise<string[]> {
    try {
      return await this.getCategoriesUseCase.getUsedCategories();
    } catch (error) {
      console.error('Error getting used categories:', error);
      throw new HttpException(
        error.message || 'Failed to get used categories',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories/available')
  @ApiOperation({ summary: 'Get all predefined available categories' })
  @ApiResponse({ 
    status: 200, 
    description: 'Available categories retrieved successfully',
    type: [String]
  })
  getAvailableCategories(): string[] {
    try {
      return this.getCategoriesUseCase.getAvailableCategories();
    } catch (error) {
      console.error('Error getting available categories:', error);
      throw new HttpException(
        error.message || 'Failed to get available categories',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
