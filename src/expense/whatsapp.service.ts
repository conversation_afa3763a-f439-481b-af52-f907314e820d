import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Expense } from './interfaces/expense.interface';

@Injectable()
export class WhatsAppService {
  private readonly numberId: string;
  private readonly token: string;
  private pendingExpenses: Map<string, Expense> = new Map(); // Almacenar gastos pendientes

  constructor(private configService: ConfigService) {
    this.numberId = this.configService.get<string>('WHATSAPP_NUMBER_ID') || '';
    this.token = this.configService.get<string>('WHATSAPP_TOKEN') || '';
  }

  // Método para generar un ID único para gastos pendientes
  private generatePendingId(): string {
    return `pending_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`;
  }

  // Método para almacenar un gasto pendiente
  storePendingExpense(expense: Expense): string {
    const pendingId = this.generatePendingId();
    this.pendingExpenses.set(pendingId, expense);
    console.log('Gasto pendiente almacenado:', { pendingId, expense });
    return pendingId;
  }

  // Método para obtener un gasto pendiente
  getPendingExpense(pendingId: string): Expense | undefined {
    return this.pendingExpenses.get(pendingId);
  }

  // Método para eliminar un gasto pendiente
  removePendingExpense(pendingId: string): void {
    this.pendingExpenses.delete(pendingId);
  }

  formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-numeric characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    console.log('Número limpio:', cleaned);

    // Check if it's an Argentine number with the format 5491XXXXXXXXXX
    if (cleaned.startsWith('5491') && cleaned.length === 13) {
      // Remove only the '9' at position 2: take "54" + everything from position 3 onwards
      const formatted = `54${cleaned.substring(3)}`;
      console.log('Número formateado (removiendo 9):', formatted);
      return formatted;
    }

    // If it's a local number without country code (10 digits)
    if (cleaned.length === 10) {
      const formatted = `54${cleaned}`;
      console.log('Número formateado (agregando código de país):', formatted);
      return formatted;
    }

    // If it's already in the correct format (54XXXXXXXXXX)
    if (cleaned.startsWith('54') && cleaned.length === 12) {
      console.log('Número ya en formato correcto:', cleaned);
      return cleaned;
    }

    console.log('Formato de número no reconocido, devolviendo sin cambios:', cleaned);
    return cleaned;
  }

  async sendMessage(to: string, message: string): Promise<boolean> {
    try {
      console.log('Enviando mensaje:', { to, message });

      if (!this.numberId || !this.token) {
        console.error('Faltan credenciales de WhatsApp');
        return false;
      }

      const response = await fetch(
        `https://graph.facebook.com/v22.0/${this.numberId}/messages`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to,
            type: 'text',
            text: {
              preview_url: false,
              body: message
            }
          })
        }
      );

      const data = await response.json();
      console.log('Respuesta de WhatsApp:', data);
      return response.status === 200;
    } catch (error) {
      console.error('Error al enviar mensaje:', error);
      return false;
    }
  }

  async sendConfirmationButtons(expense: Expense): Promise<boolean> {
    try {
      console.log('Enviando botones de confirmación:', expense);

      if (!this.numberId || !this.token) {
        console.error('Faltan credenciales de WhatsApp');
        return false;
      }

      // Almacenar el gasto pendiente y obtener un ID único
      const pendingId = this.storePendingExpense(expense);

      let bodyText = `¿Confirmas este gasto?\n\nMonto: $${expense.amount}\nCategoría: ${expense.category}\nDescripción: ${expense.description}`;

      const response = await fetch(
        `https://graph.facebook.com/v22.0/${this.numberId}/messages`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: expense.phoneNumber,
            type: 'interactive',
            interactive: {
              type: 'button',
              header: {
                type: 'text',
                text: 'Confirmar gasto'
              },
              body: {
                text: bodyText
              },
              action: {
                buttons: [
                  {
                    type: 'reply',
                    reply: {
                      id: `confirm_${pendingId}`,
                      title: 'Confirmar'
                    }
                  },
                  {
                    type: 'reply',
                    reply: {
                      id: `cancel_${pendingId}`,
                      title: 'Cancelar'
                    }
                  }
                ]
              }
            }
          })
        }
      );

      const data = await response.json();
      console.log('Respuesta de WhatsApp:', data);
      return response.status === 200;
    } catch (error) {
      console.error('Error al enviar botones de confirmación:', error);
      return false;
    }
  }

  // Método para descargar audio de WhatsApp
  async downloadAudio(audioId: string): Promise<{ buffer: Buffer; mimeType: string } | null> {
    try {
      console.log('=== DESCARGANDO AUDIO DE WHATSAPP ===');
      console.log('Audio ID:', audioId);

      if (!this.numberId || !this.token) {
        console.error('Faltan credenciales de WhatsApp');
        return null;
      }

      // Paso 1: Obtener la URL del archivo de audio
      const mediaResponse = await fetch(
        `https://graph.facebook.com/v22.0/${audioId}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.token}`,
          }
        }
      );

      if (!mediaResponse.ok) {
        console.error('Error al obtener información del audio:', mediaResponse.status);
        return null;
      }

      const mediaData = await mediaResponse.json();
      console.log('Información del audio:', mediaData);

      if (!mediaData.url) {
        console.error('No se encontró URL del audio');
        return null;
      }

      // Paso 2: Descargar el archivo de audio
      const audioResponse = await fetch(mediaData.url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
        }
      });

      if (!audioResponse.ok) {
        console.error('Error al descargar audio:', audioResponse.status);
        return null;
      }

      const audioBuffer = Buffer.from(await audioResponse.arrayBuffer());
      const mimeType = mediaData.mime_type || 'audio/ogg'; // WhatsApp suele usar OGG

      console.log('✅ Audio descargado exitosamente');
      console.log('Tamaño:', audioBuffer.length, 'bytes');
      console.log('Tipo MIME:', mimeType);

      return {
        buffer: audioBuffer,
        mimeType: mimeType
      };

    } catch (error) {
      console.error('❌ Error al descargar audio:', error);
      return null;
    }
  }

  // Método para convertir audio OGG a formato compatible con Gemini
  async convertAudioForGemini(audioBuffer: Buffer, originalMimeType: string): Promise<{ buffer: Buffer; mimeType: string }> {
    // WhatsApp envía audio en formato OGG, pero Gemini soporta varios formatos
    // Por ahora, intentamos usar el audio tal como viene
    // En el futuro, podríamos usar ffmpeg para convertir si es necesario
    
    console.log('Preparando audio para Gemini...');
    console.log('Formato original:', originalMimeType);
    
    // Mapear tipos MIME de WhatsApp a formatos soportados por Gemini
    let geminiMimeType = originalMimeType;
    
    if (originalMimeType.includes('ogg')) {
      geminiMimeType = 'audio/ogg';
    } else if (originalMimeType.includes('mp3')) {
      geminiMimeType = 'audio/mp3';
    } else if (originalMimeType.includes('wav')) {
      geminiMimeType = 'audio/wav';
    } else if (originalMimeType.includes('aac')) {
      geminiMimeType = 'audio/aac';
    } else {
      // Fallback a OGG que es común en WhatsApp
      geminiMimeType = 'audio/ogg';
    }
    
    console.log('Formato para Gemini:', geminiMimeType);
    
    return {
      buffer: audioBuffer,
      mimeType: geminiMimeType
    };
  }
} 