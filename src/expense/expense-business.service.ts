import { Injectable } from '@nestjs/common';
import { WhatsAppService } from './whatsapp.service';
import { ExpenseService } from './expense.service';
import { Expense } from './interfaces/expense.interface';
import { ExpenseData } from '../ai/interfaces/expense-data.interface';

@Injectable()
export class ExpenseBusinessService {
  constructor(
    private readonly whatsappService: WhatsAppService,
    private readonly expenseService: ExpenseService,
  ) {}

  async handleCommand(command: string, phoneNumber: string): Promise<void> {
    try {
      const lowerCommand = command.toLowerCase();
      
      // Check if it's a summary request
      if (lowerCommand.includes('resumen')) {
        await this.handleSummaryRequest(phoneNumber);
        return;
      }

      // Check if it's an expense entry
      const expenseMatch = this.parseExpenseFromText(command);
      if (expenseMatch) {
        const expense: Expense = {
          id: '',
          date: new Date().toISOString(),
          amount: expenseMatch.amount,
          category: expenseMatch.category || 'Otros',
          description: expenseMatch.description,
          phoneNumber: phoneNumber,
        };

        await this.whatsappService.sendConfirmationButtons(expense);
        return;
      }

      // Default response for unrecognized commands
      await this.whatsappService.sendMessage(
        phoneNumber,
        'No pude entender el comando. Envía un gasto con formato como:\n' +
        '• "4000 pesos una coca cola"\n' +
        '• "resumen" - para ver el resumen del mes'
      );
    } catch (error) {
      console.error('Error handling command:', error);
      await this.whatsappService.sendMessage(
        phoneNumber,
        '❌ Error al procesar el comando'
      );
    }
  }

  async handleButtonResponse(buttonId: string, phoneNumber: string): Promise<void> {
    try {
      console.log('=== MANEJANDO RESPUESTA DE BOTÓN ===');
      console.log('Button ID:', buttonId);
      console.log('Phone Number:', phoneNumber);

      if (buttonId.startsWith('confirm_')) {
        // Extraer el ID del gasto pendiente
        const pendingId = buttonId.replace('confirm_', '');
        console.log('Pending ID:', pendingId);

        // Obtener el gasto pendiente del WhatsAppService
        const expense = this.whatsappService.getPendingExpense(pendingId);
        
        if (!expense) {
          console.error('No se encontró el gasto pendiente:', pendingId);
          await this.whatsappService.sendMessage(
            phoneNumber,
            '❌ Error: No se encontró la información del gasto. Intenta enviar el gasto de nuevo.'
          );
          return;
        }

        console.log('Gasto recuperado:', expense);

        // Guardar el gasto en la base de datos
        const saved = await this.expenseService.save(expense);
        
        // Limpiar el gasto pendiente
        this.whatsappService.removePendingExpense(pendingId);
        
        if (saved) {
          await this.whatsappService.sendMessage(
            phoneNumber,
            `✅ Gasto guardado exitosamente:\n💰 $${expense.amount}\n📁 ${expense.category}\n📝 ${expense.description}`
          );
        } else {
          await this.whatsappService.sendMessage(
            phoneNumber,
            '❌ Error al guardar el gasto. Intenta de nuevo.'
          );
        }
      } else if (buttonId.startsWith('cancel_')) {
        // Extraer el ID del gasto pendiente y limpiarlo
        const pendingId = buttonId.replace('cancel_', '');
        this.whatsappService.removePendingExpense(pendingId);
        
        await this.whatsappService.sendMessage(phoneNumber, '❌ Gasto cancelado');
      }
    } catch (error) {
      console.error('Error handling button response:', error);
      await this.whatsappService.sendMessage(
        phoneNumber,
        '❌ Error al procesar la respuesta'
      );
    }
  }

  private async handleSummaryRequest(phoneNumber: string): Promise<void> {
    try {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;

      const expenses = await this.expenseService.getByYearAndMonth(year, month, undefined, phoneNumber);
      
      if (expenses.length === 0) {
        await this.whatsappService.sendMessage(
          phoneNumber,
          'No tienes gastos registrados este mes.'
        );
        return;
      }

      const total = expenses.reduce((sum, expense) => sum + expense.amount, 0);
      const summary = `📊 Resumen de ${month}/${year}\n\nTotal gastado: $${total}\nGastos registrados: ${expenses.length}`;

      await this.whatsappService.sendMessage(phoneNumber, summary);
    } catch (error) {
      console.error('Error generating summary:', error);
      await this.whatsappService.sendMessage(
        phoneNumber,
        '❌ Error al generar el resumen'
      );
    }
  }

  private parseExpenseFromText(text: string): { amount: number; description: string; category?: string } | null {
    // Simple regex to match patterns like "4000 pesos una coca cola"
    const patterns = [
      /(\d+)\s*pesos?\s+(.+)/i,
      /(\d+)\s+(.+)\s*pesos?/i,
      /(.+)\s+(\d+)\s*pesos?/i,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        const amount = parseInt(match[1]) || parseInt(match[2]);
        const description = match[1].includes(amount.toString()) ? match[2] : match[1];
        
        if (amount && description) {
          return {
            amount,
            description: description.trim(),
            category: this.categorizeExpense(description),
          };
        }
      }
    }

    return null;
  }

  private categorizeExpense(description: string): string {
    const desc = description.toLowerCase();
    
    // Food categories
    if (desc.includes('coca') || desc.includes('comida') || desc.includes('almuerzo') || 
        desc.includes('cena') || desc.includes('desayuno') || desc.includes('restaurant')) {
      return 'Alimentos';
    }
    
    // Transport
    if (desc.includes('uber') || desc.includes('taxi') || desc.includes('colectivo') || 
        desc.includes('subte') || desc.includes('transporte')) {
      return 'Transporte';
    }
    
    // Services
    if (desc.includes('luz') || desc.includes('gas') || desc.includes('agua') || 
        desc.includes('internet') || desc.includes('telefono')) {
      return 'Servicios';
    }
    
    return 'Otros';
  }

  async processStructuredMessage(text: string, phoneNumber: string): Promise<void> {
    try {
      console.log('=== PROCESANDO MENSAJE ESTRUCTURADO ===');
      console.log('Texto:', text);
      
      // Lógica para procesar mensajes en formato estructurado (formato anterior)
      // Por ejemplo: "4000 pesos coca cola"
      const parts = text.trim().split(/\s+/);
      
      if (parts.length < 2) {
        await this.whatsappService.sendMessage(
          phoneNumber,
          'Formato incorrecto. Usa: "monto descripción" (ej: "2500 coca cola")'
        );
        return;
      }

      const amount = parseFloat(parts[0]);
      if (isNaN(amount) || amount <= 0) {
        await this.whatsappService.sendMessage(
          phoneNumber,
          'El monto debe ser un número válido mayor a 0.'
        );
        return;
      }

      const description = parts.slice(1).join(' ').replace(/pesos?/i, '').trim();
      
      // Crear gasto con categoría por defecto
      const expense: Expense = {
        id: '',
        date: new Date().toISOString(),
        amount: amount,
        category: 'Otros', // Categoría por defecto para mensajes estructurados
        description: description,
        phoneNumber: phoneNumber,
      };

      console.log('Gasto estructurado creado:', expense);
      await this.whatsappService.sendConfirmationButtons(expense);

    } catch (error) {
      console.error('Error procesando mensaje estructurado:', error);
      await this.whatsappService.sendMessage(
        phoneNumber,
        'Error al procesar tu mensaje. Intenta de nuevo.'
      );
    }
  }

  async processGeminiResult(expenseData: ExpenseData, phoneNumber: string, transcription?: string): Promise<void> {
    try {
      console.log('=== PROCESANDO RESULTADO DE GEMINI ===');
      console.log('Datos del gasto:', expenseData);
      console.log('Transcripción:', transcription);

      // Verificar si los datos son válidos
      if (!expenseData.isValid) {
        let message = 'No pude extraer información de gasto de tu mensaje. ';
        
        if (transcription) {
          message += `Escuché: "${transcription}". `;
        }
        
        message += 'Intenta ser más específico sobre el monto y qué compraste. 💰📝';
        
        await this.whatsappService.sendMessage(phoneNumber, message);
        return;
      }

      // Verificar confianza mínima
      if (expenseData.confidence < 0.6) {
        console.log('Confianza muy baja, solicitando aclaración');
        await this.requestClarification(expenseData, phoneNumber, transcription);
        return;
      }

      // Crear el objeto de gasto
      const expense: Expense = {
        id: '',
        date: new Date().toISOString(),
        amount: expenseData.amount,
        category: expenseData.category,
        description: expenseData.description,
        phoneNumber: phoneNumber,
      };

      console.log('Enviando confirmación de gasto extraído:', expense);
      
      // Enviar mensaje adicional si hay transcripción
      if (transcription) {
        await this.whatsappService.sendMessage(
          phoneNumber,
          `✅ Entendí tu gasto de $${expense.amount} en ${expense.category.toLowerCase()}`
        );
      }
      
      await this.whatsappService.sendConfirmationButtons(expense);

    } catch (error) {
      console.error('Error procesando resultado de Gemini:', error);
      await this.whatsappService.sendMessage(
        phoneNumber,
        'Error al procesar la información del gasto. Intenta de nuevo.'
      );
    }
  }

  private async requestClarification(expenseData: ExpenseData, phoneNumber: string, transcription?: string): Promise<void> {
    let clarificationMessage = '🤔 Entendí parcialmente tu mensaje:\n\n';
    
    if (transcription) {
      clarificationMessage += `🎤 Escuché: "${transcription}"\n\n`;
    }
    
    if (expenseData.amount > 0) {
      clarificationMessage += `💰 Monto: $${expenseData.amount}\n`;
    }
    
    if (expenseData.category) {
      clarificationMessage += `📁 Categoría: ${expenseData.category}\n`;
    }
    
    if (expenseData.description) {
      clarificationMessage += `📝 Descripción: ${expenseData.description}\n`;
    }
    
    clarificationMessage += '\n¿Podrías ser más específico? Por ejemplo:\n';
    clarificationMessage += '"Gasté 2500 pesos en cocas y queso en el almacén"';
    
    await this.whatsappService.sendMessage(phoneNumber, clarificationMessage);
  }
} 