import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from '@upstash/redis';
import { Expense } from './interfaces/expense.interface';
import { categories } from './config/categories.config';

@Injectable()
export class ExpenseService {
  private readonly expenseRepository: Redis;

  constructor(private configService: ConfigService) {
    this.expenseRepository = new Redis({
      url: this.configService.get<string>('UPSTASH_REDIS_URL'),
      token: this.configService.get<string>('UPSTASH_REDIS_TOKEN'),
    });
  }

  async getByYearAndMonth(year: number, month: number, category?: string, phoneNumber?: string): Promise<Expense[]> {
    console.log(`=== BUSCANDO GASTOS ===`);
    console.log(`Año: ${year}, Mes: ${month}`);
    console.log(`Categoría: ${category}, Teléfono: ${phoneNumber}`);
    
    const monthKey = `expenses:${year}:${month}`;
    console.log(`Clave de búsqueda: ${monthKey}`);
    
    const expenses = await this.expenseRepository.get<Expense[]>(monthKey) || [];
    console.log(`Gastos encontrados en Redis: ${expenses.length}`);
    
    let filteredExpenses = expenses;
    
    if (category && category !== 'all') {
      filteredExpenses = filteredExpenses.filter(expense => expense.category === category);
      console.log(`Después de filtrar por categoría: ${filteredExpenses.length}`);
    }

    if (phoneNumber) {
      filteredExpenses = filteredExpenses.filter(expense => expense.phoneNumber === phoneNumber);
      console.log(`Después de filtrar por teléfono: ${filteredExpenses.length}`);
    }

    return filteredExpenses;
  }

  async getPendingInstallments(year: number, month: number): Promise<Expense[]> {
    const allExpenses = await this.expenseRepository.get<Expense[]>('expenses') || [];
    
    return allExpenses.filter(expense => {
      if (!expense.installment_details) return false;

      const expenseDate = new Date(expense.date);
      const selectedDate = new Date(year, month, 1);

      return expenseDate > selectedDate;
    });
  }

  async getInstallmentGroup(groupId: string): Promise<Expense[]> {
    const allExpenses = await this.expenseRepository.get<Expense[]>('expenses') || [];
    
    const groupExpenses = allExpenses.filter(expense => {
      return expense.installment_details && expense.installment_details.group_id === groupId;
    });

    return groupExpenses.sort((a, b) => {
      if (a.installment_details && b.installment_details) {
        return a.installment_details.current_installment - b.installment_details.current_installment;
      }
      return 0;
    });
  }

  async getAvailableYears(): Promise<number[]> {
    const allExpenses = await this.expenseRepository.get<Expense[]>('expenses') || [];
    
    const years = [...new Set(allExpenses.map(expense => new Date(expense.date).getFullYear()))];
    return years.sort((a, b) => b - a);
  }

  async getCategories(): Promise<string[]> {
    const allExpenses = await this.expenseRepository.get<Expense[]>('expenses') || [];
    const expenseCategories = [...new Set(allExpenses.map(expense => expense.category))];
    
    const allCategories = [...new Set([...Object.keys(categories), ...expenseCategories])];
    return allCategories.sort();
  }

  async save(expense: Expense): Promise<boolean> {
    try {
      console.log('=== GUARDANDO GASTO EN REDIS ===');
      console.log('Gasto a guardar:', expense);

      // Generar ID único si no tiene
      if (!expense.id) {
        expense.id = this.generateExpenseId();
      }

      // Crear clave única para el gasto
      const key = `expense:${expense.phoneNumber}:${expense.id}`;
      console.log('Clave Redis:', key);

      // Guardar el gasto individual
      await this.expenseRepository.set(key, expense);

      // También mantener una lista por año/mes para consultas rápidas
      const expenseDate = new Date(expense.date);
      const year = expenseDate.getFullYear();
      const month = expenseDate.getMonth() + 1; // Convertir a 1-indexado para consistencia
      const monthKey = `expenses:${year}:${month}`;

      console.log(`Guardando en clave mensual: ${monthKey}`);

      // Obtener gastos existentes del mes
      const monthlyExpenses = await this.expenseRepository.get<Expense[]>(monthKey) || [];
      
      // Agregar el nuevo gasto
      monthlyExpenses.push(expense);
      
      // Guardar la lista actualizada
      await this.expenseRepository.set(monthKey, monthlyExpenses);

      console.log('✅ Gasto guardado exitosamente');
      return true;
    } catch (error) {
      console.error('❌ Error al guardar gasto en Redis:', error);
      return false;
    }
  }

  private generateExpenseId(): string {
    return `exp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }
} 