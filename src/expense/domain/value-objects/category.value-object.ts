import { InvalidCategoryException } from '../../../common/exceptions/domain.exception';

export enum ExpenseCategory {
  ALIMENTOS = 'Alimentos',
  TRANSPORTE = 'Transporte',
  SERVICIOS = 'Servicios',
  ENTRETENIMIENTO = 'Entretenimiento',
  SALUD = 'Salud',
  EDUCACION = 'Educación',
  HOGAR = 'Hogar',
  OTROS = 'Otros',
}

export class Category {
  private readonly _value: ExpenseCategory;

  constructor(value: string) {
    const normalizedValue = this.normalizeCategory(value);
    if (!Object.values(ExpenseCategory).includes(normalizedValue as ExpenseCategory)) {
      throw new InvalidCategoryException(`Invalid category: ${value}`);
    }
    this._value = normalizedValue as ExpenseCategory;
  }

  get value(): ExpenseCategory {
    return this._value;
  }

  equals(other: Category): boolean {
    return this._value === other._value;
  }

  toString(): string {
    return this._value;
  }

  toJSON(): string {
    return this._value;
  }

  private normalizeCategory(value: string): string {
    const categoryMap: Record<string, ExpenseCategory> = {
      'alimentos': ExpenseCategory.ALIMENTOS,
      'comida': ExpenseCategory.ALIMENTOS,
      'supermercado': ExpenseCategory.ALIMENTOS,
      'transporte': ExpenseCategory.TRANSPORTE,
      'servicios': ExpenseCategory.SERVICIOS,
      'entretenimiento': ExpenseCategory.ENTRETENIMIENTO,
      'salud': ExpenseCategory.SALUD,
      'educacion': ExpenseCategory.EDUCACION,
      'educación': ExpenseCategory.EDUCACION,
      'hogar': ExpenseCategory.HOGAR,
      'otros': ExpenseCategory.OTROS,
    };

    const normalized = value.toLowerCase().trim();
    return categoryMap[normalized] || ExpenseCategory.OTROS;
  }

  static fromString(value: string): Category {
    return new Category(value);
  }

  static getAvailableCategories(): ExpenseCategory[] {
    return Object.values(ExpenseCategory);
  }

  static isValidCategory(value: string): boolean {
    try {
      new Category(value);
      return true;
    } catch {
      return false;
    }
  }
}
