import { InvalidAmountException } from '../../../common/exceptions/domain.exception';

export class Money {
  private readonly _amount: number;

  constructor(amount: number) {
    if (amount < 0) {
      throw new InvalidAmountException('Amount cannot be negative');
    }
    if (!Number.isFinite(amount)) {
      throw new InvalidAmountException('Amount must be a finite number');
    }
    this._amount = Math.round(amount * 100) / 100; // Round to 2 decimal places
  }

  get amount(): number {
    return this._amount;
  }

  add(other: Money): Money {
    return new Money(this._amount + other._amount);
  }

  subtract(other: Money): Money {
    return new Money(this._amount - other._amount);
  }

  multiply(factor: number): Money {
    return new Money(this._amount * factor);
  }

  equals(other: Money): boolean {
    return this._amount === other._amount;
  }

  isGreaterThan(other: Money): boolean {
    return this._amount > other._amount;
  }

  isLessThan(other: Money): boolean {
    return this._amount < other._amount;
  }

  toString(): string {
    return `$${this._amount.toFixed(2)}`;
  }

  toJSON(): number {
    return this._amount;
  }

  static fromNumber(amount: number): Money {
    return new Money(amount);
  }

  static zero(): Money {
    return new Money(0);
  }
}
