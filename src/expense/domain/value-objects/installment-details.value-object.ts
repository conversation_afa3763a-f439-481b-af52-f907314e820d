export class InstallmentDetails {
  private readonly _groupId: string;
  private readonly _currentInstallment: number;
  private readonly _totalInstallments: number;

  constructor(
    groupId: string,
    currentInstallment: number,
    totalInstallments: number
  ) {
    if (!groupId || groupId.trim().length === 0) {
      throw new Error('Group ID cannot be empty');
    }
    if (currentInstallment < 1) {
      throw new Error('Current installment must be greater than 0');
    }
    if (totalInstallments < 1) {
      throw new Error('Total installments must be greater than 0');
    }
    if (currentInstallment > totalInstallments) {
      throw new Error('Current installment cannot be greater than total installments');
    }

    this._groupId = groupId.trim();
    this._currentInstallment = currentInstallment;
    this._totalInstallments = totalInstallments;
  }

  get groupId(): string {
    return this._groupId;
  }

  get currentInstallment(): number {
    return this._currentInstallment;
  }

  get totalInstallments(): number {
    return this._totalInstallments;
  }

  get remainingInstallments(): number {
    return this._totalInstallments - this._currentInstallment;
  }

  get isLastInstallment(): boolean {
    return this._currentInstallment === this._totalInstallments;
  }

  get isFirstInstallment(): boolean {
    return this._currentInstallment === 1;
  }

  get progressPercentage(): number {
    return Math.round((this._currentInstallment / this._totalInstallments) * 100);
  }

  equals(other: InstallmentDetails): boolean {
    return (
      this._groupId === other._groupId &&
      this._currentInstallment === other._currentInstallment &&
      this._totalInstallments === other._totalInstallments
    );
  }

  toString(): string {
    return `${this._currentInstallment}/${this._totalInstallments}`;
  }

  toJSON(): {
    group_id: string;
    current_installment: number;
    total_installments: number;
  } {
    return {
      group_id: this._groupId,
      current_installment: this._currentInstallment,
      total_installments: this._totalInstallments,
    };
  }

  static fromJSON(data: {
    group_id: string;
    current_installment: number;
    total_installments: number;
  }): InstallmentDetails {
    return new InstallmentDetails(
      data.group_id,
      data.current_installment,
      data.total_installments
    );
  }

  static generateGroupId(): string {
    return `installment_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }
}
