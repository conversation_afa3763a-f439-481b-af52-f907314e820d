export class PhoneNumber {
  private readonly _value: string;

  constructor(value: string) {
    const normalized = this.normalizePhoneNumber(value);
    if (!this.isValidPhoneNumber(normalized)) {
      throw new Error(`Invalid phone number: ${value}`);
    }
    this._value = normalized;
  }

  get value(): string {
    return this._value;
  }

  equals(other: PhoneNumber): boolean {
    return this._value === other._value;
  }

  toString(): string {
    return this._value;
  }

  toJSON(): string {
    return this._value;
  }

  private normalizePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');
    
    // Remove leading +54 9 if present
    if (cleaned.startsWith('549')) {
      cleaned = cleaned.substring(3);
    } else if (cleaned.startsWith('54')) {
      cleaned = cleaned.substring(2);
    }
    
    // Ensure it starts with 54 for Argentina
    if (!cleaned.startsWith('54')) {
      cleaned = '54' + cleaned;
    }
    
    return cleaned;
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic validation for Argentine phone numbers
    // Should start with 54 and have appropriate length
    const argentinePattern = /^54\d{8,10}$/;
    return argentinePattern.test(phoneNumber);
  }

  static fromString(value: string): PhoneNumber {
    return new PhoneNumber(value);
  }

  static isValid(value: string): boolean {
    try {
      new PhoneNumber(value);
      return true;
    } catch {
      return false;
    }
  }
}
