import { Expense } from '../entities/expense.entity';
import { PhoneNumber } from '../value-objects/phone-number.value-object';
import { Category } from '../value-objects/category.value-object';

export interface ExpenseFilters {
  year?: number;
  month?: number;
  category?: Category;
  phoneNumber?: PhoneNumber;
  startDate?: Date;
  endDate?: Date;
}

export interface ExpenseRepository {
  /**
   * Save an expense to the repository
   */
  save(expense: Expense): Promise<void>;

  /**
   * Find an expense by its ID
   */
  findById(id: string): Promise<Expense | null>;

  /**
   * Find expenses by filters
   */
  findByFilters(filters: ExpenseFilters): Promise<Expense[]>;

  /**
   * Find expenses by year and month
   */
  findByYearAndMonth(year: number, month: number): Promise<Expense[]>;

  /**
   * Find expenses by phone number
   */
  findByPhoneNumber(phoneNumber: PhoneNumber): Promise<Expense[]>;

  /**
   * Find expenses by category
   */
  findByCategory(category: Category): Promise<Expense[]>;

  /**
   * Find expenses in a date range
   */
  findByDateRange(startDate: Date, endDate: Date): Promise<Expense[]>;

  /**
   * Find all expenses in an installment group
   */
  findByInstallmentGroup(groupId: string): Promise<Expense[]>;

  /**
   * Find pending installments for a given period
   */
  findPendingInstallments(year: number, month: number): Promise<Expense[]>;

  /**
   * Get all available years that have expenses
   */
  getAvailableYears(): Promise<number[]>;

  /**
   * Get all categories that have been used
   */
  getUsedCategories(): Promise<Category[]>;

  /**
   * Delete an expense by ID
   */
  delete(id: string): Promise<void>;

  /**
   * Check if an expense exists
   */
  exists(id: string): Promise<boolean>;

  /**
   * Count expenses by filters
   */
  countByFilters(filters: ExpenseFilters): Promise<number>;

  /**
   * Get total amount by filters
   */
  getTotalAmountByFilters(filters: ExpenseFilters): Promise<number>;
}
