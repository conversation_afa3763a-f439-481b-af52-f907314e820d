import { Money } from '../value-objects/money.value-object';
import { Category } from '../value-objects/category.value-object';
import { PhoneNumber } from '../value-objects/phone-number.value-object';
import { InstallmentDetails } from '../value-objects/installment-details.value-object';
import { InvalidExpenseException } from '../../../common/exceptions/domain.exception';

export class Expense {
  private readonly _id: string;
  private readonly _date: Date;
  private readonly _amount: Money;
  private readonly _category: Category;
  private readonly _description: string;
  private readonly _phoneNumber: PhoneNumber;
  private readonly _installmentDetails?: InstallmentDetails;

  constructor(
    id: string,
    date: Date,
    amount: Money,
    category: Category,
    description: string,
    phoneNumber: PhoneNumber,
    installmentDetails?: InstallmentDetails
  ) {
    if (!id || id.trim().length === 0) {
      throw new InvalidExpenseException('Expense ID cannot be empty');
    }
    if (!description || description.trim().length === 0) {
      throw new InvalidExpenseException('Expense description cannot be empty');
    }
    if (description.trim().length > 500) {
      throw new InvalidExpenseException('Expense description cannot exceed 500 characters');
    }

    this._id = id.trim();
    this._date = new Date(date);
    this._amount = amount;
    this._category = category;
    this._description = description.trim();
    this._phoneNumber = phoneNumber;
    this._installmentDetails = installmentDetails;
  }

  get id(): string {
    return this._id;
  }

  get date(): Date {
    return new Date(this._date);
  }

  get amount(): Money {
    return this._amount;
  }

  get category(): Category {
    return this._category;
  }

  get description(): string {
    return this._description;
  }

  get phoneNumber(): PhoneNumber {
    return this._phoneNumber;
  }

  get installmentDetails(): InstallmentDetails | undefined {
    return this._installmentDetails;
  }

  get isInstallment(): boolean {
    return this._installmentDetails !== undefined;
  }

  get year(): number {
    return this._date.getFullYear();
  }

  get month(): number {
    return this._date.getMonth() + 1; // 1-indexed
  }

  equals(other: Expense): boolean {
    return this._id === other._id;
  }

  belongsToSameInstallmentGroup(other: Expense): boolean {
    if (!this.isInstallment || !other.isInstallment) {
      return false;
    }
    return this._installmentDetails!.groupId === other._installmentDetails!.groupId;
  }

  isFromPhoneNumber(phoneNumber: PhoneNumber): boolean {
    return this._phoneNumber.equals(phoneNumber);
  }

  isInCategory(category: Category): boolean {
    return this._category.equals(category);
  }

  isInDateRange(startDate: Date, endDate: Date): boolean {
    return this._date >= startDate && this._date <= endDate;
  }

  isInYearAndMonth(year: number, month: number): boolean {
    return this.year === year && this.month === month;
  }

  toString(): string {
    const installmentInfo = this.isInstallment 
      ? ` (${this._installmentDetails!.toString()})`
      : '';
    return `${this._amount.toString()} - ${this._category.toString()} - ${this._description}${installmentInfo}`;
  }

  toJSON(): {
    id: string;
    date: string;
    amount: number;
    category: string;
    description: string;
    phoneNumber: string;
    installment_details?: {
      group_id: string;
      current_installment: number;
      total_installments: number;
    };
  } {
    return {
      id: this._id,
      date: this._date.toISOString(),
      amount: this._amount.toJSON(),
      category: this._category.toJSON(),
      description: this._description,
      phoneNumber: this._phoneNumber.toJSON(),
      installment_details: this._installmentDetails?.toJSON(),
    };
  }

  static generateId(): string {
    return `expense_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  static fromJSON(data: {
    id: string;
    date: string;
    amount: number;
    category: string;
    description: string;
    phoneNumber: string;
    installment_details?: {
      group_id: string;
      current_installment: number;
      total_installments: number;
    };
  }): Expense {
    return new Expense(
      data.id,
      new Date(data.date),
      Money.fromNumber(data.amount),
      Category.fromString(data.category),
      data.description,
      PhoneNumber.fromString(data.phoneNumber),
      data.installment_details ? InstallmentDetails.fromJSON(data.installment_details) : undefined
    );
  }

  static create(
    amount: Money,
    category: Category,
    description: string,
    phoneNumber: PhoneNumber,
    installmentDetails?: InstallmentDetails
  ): Expense {
    return new Expense(
      Expense.generateId(),
      new Date(),
      amount,
      category,
      description,
      phoneNumber,
      installmentDetails
    );
  }
}
