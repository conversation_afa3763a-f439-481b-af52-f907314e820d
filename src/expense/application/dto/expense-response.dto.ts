export class InstallmentDetailsResponseDto {
  group_id: string;
  current_installment: number;
  total_installments: number;
  remaining_installments: number;
  is_last_installment: boolean;
  progress_percentage: number;

  constructor(
    groupId: string,
    currentInstallment: number,
    totalInstallments: number,
    remainingInstallments: number,
    isLastInstallment: boolean,
    progressPercentage: number
  ) {
    this.group_id = groupId;
    this.current_installment = currentInstallment;
    this.total_installments = totalInstallments;
    this.remaining_installments = remainingInstallments;
    this.is_last_installment = isLastInstallment;
    this.progress_percentage = progressPercentage;
  }
}

export class ExpenseResponseDto {
  id: string;
  date: string;
  amount: number;
  category: string;
  description: string;
  phoneNumber: string;
  year: number;
  month: number;
  isInstallment: boolean;
  installment_details?: InstallmentDetailsResponseDto;

  constructor(
    id: string,
    date: string,
    amount: number,
    category: string,
    description: string,
    phoneNumber: string,
    year: number,
    month: number,
    isInstallment: boolean,
    installmentDetails?: InstallmentDetailsResponseDto
  ) {
    this.id = id;
    this.date = date;
    this.amount = amount;
    this.category = category;
    this.description = description;
    this.phoneNumber = phoneNumber;
    this.year = year;
    this.month = month;
    this.isInstallment = isInstallment;
    this.installment_details = installmentDetails;
  }
}

export class ExpenseListResponseDto {
  expenses: ExpenseResponseDto[];
  total: number;
  totalAmount: number;
  filters: {
    year?: number;
    month?: number;
    category?: string;
    phoneNumber?: string;
    startDate?: string;
    endDate?: string;
  };

  constructor(
    expenses: ExpenseResponseDto[],
    total: number,
    totalAmount: number,
    filters: {
      year?: number;
      month?: number;
      category?: string;
      phoneNumber?: string;
      startDate?: string;
      endDate?: string;
    }
  ) {
    this.expenses = expenses;
    this.total = total;
    this.totalAmount = totalAmount;
    this.filters = filters;
  }
}

export class ExpenseSummaryDto {
  totalExpenses: number;
  totalAmount: number;
  averageAmount: number;
  categorySummary: Record<string, { count: number; amount: number }>;
  monthlyTrend: Record<string, number>;

  constructor(
    totalExpenses: number,
    totalAmount: number,
    averageAmount: number,
    categorySummary: Record<string, { count: number; amount: number }>,
    monthlyTrend: Record<string, number>
  ) {
    this.totalExpenses = totalExpenses;
    this.totalAmount = totalAmount;
    this.averageAmount = averageAmount;
    this.categorySummary = categorySummary;
    this.monthlyTrend = monthlyTrend;
  }
}
