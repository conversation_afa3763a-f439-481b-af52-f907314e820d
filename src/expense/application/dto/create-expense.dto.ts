import { <PERSON>N<PERSON>ber, IsString, <PERSON><PERSON><PERSON><PERSON>, IsPos<PERSON>, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class CreateInstallmentDetailsDto {
  @IsString()
  @MinLength(1)
  group_id: string;

  @IsNumber()
  @IsPositive()
  current_installment: number;

  @IsNumber()
  @IsPositive()
  total_installments: number;
}

export class CreateExpenseDto {
  @IsNumber()
  @IsPositive()
  amount: number;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  category: string;

  @IsString()
  @MinLength(1)
  @MaxLength(500)
  description: string;

  @IsString()
  @MinLength(1)
  phoneNumber: string;

  @IsOptional()
  installment_details?: CreateInstallmentDetailsDto;
}
