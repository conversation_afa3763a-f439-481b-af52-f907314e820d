import { Injectable, Inject } from '@nestjs/common';
import { ExpenseRepository } from '../../domain/repositories/expense.repository';
import { Expense } from '../../domain/entities/expense.entity';
import { Money } from '../../domain/value-objects/money.value-object';
import { Category } from '../../domain/value-objects/category.value-object';
import { PhoneNumber } from '../../domain/value-objects/phone-number.value-object';
import { InstallmentDetails } from '../../domain/value-objects/installment-details.value-object';
import { CreateExpenseDto } from '../dto/create-expense.dto';
import { ExpenseResponseDto, InstallmentDetailsResponseDto } from '../dto/expense-response.dto';
import { AppLoggerService } from '../../../common/services/logger.service';
import { MetricsService } from '../../../common/services/metrics.service';

@Injectable()
export class CreateExpenseUseCase {
  constructor(
    @Inject('ExpenseRepository') private readonly expenseRepository: ExpenseRepository,
    private readonly logger: AppLoggerService,
    private readonly metrics: MetricsService,
  ) {
    this.logger.setContext('CreateExpenseUseCase');
  }

  async execute(createExpenseDto: CreateExpenseDto): Promise<ExpenseResponseDto> {
    const stopTimer = this.metrics.startTimer('expense.create');

    try {
      this.logger.info('Creating new expense', {
        operation: 'CREATE_EXPENSE',
        amount: createExpenseDto.amount,
        category: createExpenseDto.category,
        phoneNumber: createExpenseDto.phoneNumber,
      });

      // Create value objects from DTO
      const amount = Money.fromNumber(createExpenseDto.amount);
      const category = Category.fromString(createExpenseDto.category);
      const phoneNumber = PhoneNumber.fromString(createExpenseDto.phoneNumber);

      let installmentDetails: InstallmentDetails | undefined;
      if (createExpenseDto.installment_details) {
        installmentDetails = new InstallmentDetails(
          createExpenseDto.installment_details.group_id,
          createExpenseDto.installment_details.current_installment,
          createExpenseDto.installment_details.total_installments
        );
      }

      // Create expense entity
      const expense = Expense.create(
        amount,
        category,
        createExpenseDto.description,
        phoneNumber,
        installmentDetails
      );

      // Save to repository
      await this.expenseRepository.save(expense);

      // Log success and record metrics
      this.logger.logExpenseCreated(expense.id, expense.amount.amount, expense.phoneNumber.value);
      this.metrics.recordExpenseCreated(expense.category.value, expense.amount.amount);

      // Return response DTO
      return this.mapToResponseDto(expense);
    } catch (error) {
      this.logger.error('Failed to create expense', error.stack, {
        operation: 'CREATE_EXPENSE',
        amount: createExpenseDto.amount,
        category: createExpenseDto.category,
        error: error.message,
      });
      throw error;
    } finally {
      stopTimer();
    }
  }

  private mapToResponseDto(expense: Expense): ExpenseResponseDto {
    let installmentDetailsDto: InstallmentDetailsResponseDto | undefined;
    
    if (expense.installmentDetails) {
      installmentDetailsDto = new InstallmentDetailsResponseDto(
        expense.installmentDetails.groupId,
        expense.installmentDetails.currentInstallment,
        expense.installmentDetails.totalInstallments,
        expense.installmentDetails.remainingInstallments,
        expense.installmentDetails.isLastInstallment,
        expense.installmentDetails.progressPercentage
      );
    }

    return new ExpenseResponseDto(
      expense.id,
      expense.date.toISOString(),
      expense.amount.amount,
      expense.category.value,
      expense.description,
      expense.phoneNumber.value,
      expense.year,
      expense.month,
      expense.isInstallment,
      installmentDetailsDto
    );
  }
}
