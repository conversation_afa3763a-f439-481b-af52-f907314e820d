import { Injectable, Inject } from '@nestjs/common';
import { ExpenseRepository } from '../../domain/repositories/expense.repository';
import { Category } from '../../domain/value-objects/category.value-object';

@Injectable()
export class GetCategoriesUseCase {
  constructor(@Inject('ExpenseRepository') private readonly expenseRepository: ExpenseRepository) {}

  async execute(): Promise<string[]> {
    // Get all available categories (both predefined and used ones)
    const availableCategories = Category.getAvailableCategories();
    const usedCategories = await this.expenseRepository.getUsedCategories();
    
    // Combine and deduplicate
    const allCategories = new Set([
      ...availableCategories,
      ...usedCategories.map(cat => cat.value)
    ]);

    return Array.from(allCategories).sort();
  }

  async getUsedCategories(): Promise<string[]> {
    const usedCategories = await this.expenseRepository.getUsedCategories();
    return usedCategories.map(cat => cat.value).sort();
  }

  getAvailableCategories(): string[] {
    return Category.getAvailableCategories().sort();
  }
}
