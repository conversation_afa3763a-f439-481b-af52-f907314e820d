import { Injectable } from '@nestjs/common';
import { ExpenseRepository, ExpenseFilters } from '../../domain/repositories/expense.repository';
import { Expense } from '../../domain/entities/expense.entity';
import { Category } from '../../domain/value-objects/category.value-object';
import { PhoneNumber } from '../../domain/value-objects/phone-number.value-object';
import { ExpenseFilterDto } from '../dto/expense-filter.dto';
import { ExpenseListResponseDto, ExpenseResponseDto, InstallmentDetailsResponseDto } from '../dto/expense-response.dto';

@Injectable()
export class GetExpensesUseCase {
  constructor(private readonly expenseRepository: ExpenseRepository) {}

  async execute(filterDto: ExpenseFilterDto): Promise<ExpenseListResponseDto> {
    // Convert DTO to domain filters
    const filters = this.mapToFilters(filterDto);

    // Get expenses from repository
    const expenses = await this.expenseRepository.findByFilters(filters);
    
    // Calculate totals
    const total = expenses.length;
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount.amount, 0);

    // Map to response DTOs
    const expenseResponseDtos = expenses.map(expense => this.mapToResponseDto(expense));

    return new ExpenseListResponseDto(
      expenseResponseDtos,
      total,
      totalAmount,
      {
        year: filterDto.year,
        month: filterDto.month,
        category: filterDto.category,
        phoneNumber: filterDto.phoneNumber,
        startDate: filterDto.startDate,
        endDate: filterDto.endDate,
      }
    );
  }

  async getByYearAndMonth(year: number, month: number): Promise<ExpenseListResponseDto> {
    const expenses = await this.expenseRepository.findByYearAndMonth(year, month);
    
    const total = expenses.length;
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount.amount, 0);
    const expenseResponseDtos = expenses.map(expense => this.mapToResponseDto(expense));

    return new ExpenseListResponseDto(
      expenseResponseDtos,
      total,
      totalAmount,
      { year, month }
    );
  }

  async getPendingInstallments(year: number, month: number): Promise<ExpenseListResponseDto> {
    const expenses = await this.expenseRepository.findPendingInstallments(year, month);
    
    const total = expenses.length;
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount.amount, 0);
    const expenseResponseDtos = expenses.map(expense => this.mapToResponseDto(expense));

    return new ExpenseListResponseDto(
      expenseResponseDtos,
      total,
      totalAmount,
      { year, month }
    );
  }

  async getInstallmentGroup(groupId: string): Promise<ExpenseListResponseDto> {
    const expenses = await this.expenseRepository.findByInstallmentGroup(groupId);
    
    const total = expenses.length;
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount.amount, 0);
    const expenseResponseDtos = expenses.map(expense => this.mapToResponseDto(expense));

    return new ExpenseListResponseDto(
      expenseResponseDtos,
      total,
      totalAmount,
      {}
    );
  }

  private mapToFilters(filterDto: ExpenseFilterDto): ExpenseFilters {
    const filters: ExpenseFilters = {};

    if (filterDto.year !== undefined) {
      filters.year = filterDto.year;
    }

    if (filterDto.month !== undefined) {
      filters.month = filterDto.month;
    }

    if (filterDto.category) {
      filters.category = Category.fromString(filterDto.category);
    }

    if (filterDto.phoneNumber) {
      filters.phoneNumber = PhoneNumber.fromString(filterDto.phoneNumber);
    }

    if (filterDto.startDate) {
      filters.startDate = new Date(filterDto.startDate);
    }

    if (filterDto.endDate) {
      filters.endDate = new Date(filterDto.endDate);
    }

    return filters;
  }

  private mapToResponseDto(expense: Expense): ExpenseResponseDto {
    let installmentDetailsDto: InstallmentDetailsResponseDto | undefined;
    
    if (expense.installmentDetails) {
      installmentDetailsDto = new InstallmentDetailsResponseDto(
        expense.installmentDetails.groupId,
        expense.installmentDetails.currentInstallment,
        expense.installmentDetails.totalInstallments,
        expense.installmentDetails.remainingInstallments,
        expense.installmentDetails.isLastInstallment,
        expense.installmentDetails.progressPercentage
      );
    }

    return new ExpenseResponseDto(
      expense.id,
      expense.date.toISOString(),
      expense.amount.amount,
      expense.category.value,
      expense.description,
      expense.phoneNumber.value,
      expense.year,
      expense.month,
      expense.isInstallment,
      installmentDetailsDto
    );
  }
}
