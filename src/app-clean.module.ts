import { Module } from '@nestjs/common';

// Common/Cross-cutting modules
import { ConfigModule } from './config/config.module';
import { CommonModule } from './common/common.module';

// Clean Architecture modules
import { ExpenseCleanModule } from './expense/expense-clean.module';
import { AiCleanModule } from './ai/ai-clean.module';
import { WebhookCleanModule } from './webhook/webhook-clean.module';

// Keep original controllers for backward compatibility if needed
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Core modules
    ConfigModule,
    CommonModule,
    
    // Business modules (Clean Architecture)
    ExpenseCleanModule,
    AiCleanModule,
    WebhookCleanModule,
  ],
  controllers: [AppController], // Keep for health checks, etc.
  providers: [AppService],
})
export class AppCleanModule {}
