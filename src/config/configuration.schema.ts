import * as Jo<PERSON> from 'joi';

export const configurationSchema = Joi.object({
  // Server configuration
  PORT: Joi.number().default(3000),
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),

  // Redis configuration
  UPSTASH_REDIS_URL: Joi.string().required(),
  UPSTASH_REDIS_TOKEN: Joi.string().required(),

  // WhatsApp configuration
  WHATSAPP_NUMBER_ID: Joi.string().required(),
  WHATSAPP_TOKEN: Joi.string().required(),
  VERIFY_TOKEN: Joi.string().required(),

  // AI configuration
  GEMINI_API_KEY: Joi.string().required(),

  // Logging configuration
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug')
    .default('info'),
  
  // Application configuration
  MAX_AUDIO_SIZE_MB: Joi.number().default(10),
  MIN_CONFIDENCE_THRESHOLD: Joi.number().min(0).max(1).default(0.6),
  MAX_EXPENSE_AMOUNT: Joi.number().default(1000000),
});

export interface Configuration {
  port: number;
  nodeEnv: string;
  upstashRedisUrl: string;
  upstashRedisToken: string;
  whatsappNumberId: string;
  whatsappToken: string;
  verifyToken: string;
  geminiApiKey: string;
  logLevel: string;
  maxAudioSizeMb: number;
  minConfidenceThreshold: number;
  maxExpenseAmount: number;
}

export const configuration = (): Configuration => ({
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  upstashRedisUrl: process.env.UPSTASH_REDIS_URL || '',
  upstashRedisToken: process.env.UPSTASH_REDIS_TOKEN || '',
  whatsappNumberId: process.env.WHATSAPP_NUMBER_ID || '',
  whatsappToken: process.env.WHATSAPP_TOKEN || '',
  verifyToken: process.env.VERIFY_TOKEN || '',
  geminiApiKey: process.env.GEMINI_API_KEY || '',
  logLevel: process.env.LOG_LEVEL || 'info',
  maxAudioSizeMb: parseInt(process.env.MAX_AUDIO_SIZE_MB || '10', 10),
  minConfidenceThreshold: parseFloat(process.env.MIN_CONFIDENCE_THRESHOLD || '0.6'),
  maxExpenseAmount: parseInt(process.env.MAX_EXPENSE_AMOUNT || '1000000', 10),
});
