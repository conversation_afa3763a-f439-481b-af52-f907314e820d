import { Controller, Get, Post, Body, Query, Res, HttpStatus, ValidationPipe, Inject } from '@nestjs/common';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ProcessTextMessageUseCase } from '../../application/use-cases/process-text-message.use-case';
import { ProcessAudioMessageUseCase } from '../../application/use-cases/process-audio-message.use-case';
import { ProcessInteractiveMessageUseCase } from '../../application/use-cases/process-interactive-message.use-case';
import { WebhookMessageDto } from '../dto/webhook-message.dto';
import { IAiProcessor } from '../../../ai/domain/interfaces/ai-processor.interface';

@Controller('webhook')
export class WebhookController {
  constructor(
    private readonly configService: ConfigService,
    private readonly processTextMessageUseCase: ProcessTextMessageUseCase,
    private readonly processAudioMessageUseCase: ProcessAudioMessageUseCase,
    private readonly processInteractiveMessageUseCase: ProcessInteractiveMessageUseCase,
    @Inject('IAiProcessor') private readonly aiProcessor: IAiProcessor,
  ) {}

  @Get()
  verifyWebhook(@Query() query: any, @Res() res: Response) {
    console.log('=== WEBHOOK VERIFICATION ===');
    console.log('Query received:', JSON.stringify(query, null, 2));
    
    const mode = query['hub.mode'];
    const token = query['hub.verify_token'];
    const challenge = query['hub.challenge'];

    console.log('Extracted parameters:');
    console.log('- Mode:', mode);
    console.log('- Token received:', token);
    console.log('- Challenge:', challenge);

    const verifyToken = this.configService.get<string>('VERIFY_TOKEN');
    console.log('- Expected token:', verifyToken);

    // Check that all required parameters are present
    if (!mode || !token || !challenge) {
      console.log('❌ Missing required parameters');
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Missing required parameters',
        received: { 
          mode, 
          token: token ? 'present' : 'missing', 
          challenge: challenge ? 'present' : 'missing' 
        }
      });
    }

    // Check that mode is correct
    if (mode !== 'subscribe') {
      console.log('❌ Invalid mode:', mode);
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Invalid mode',
        received: mode,
        expected: 'subscribe'
      });
    }

    // Verify token
    if (token !== verifyToken) {
      console.log('❌ Token mismatch');
      console.log('Received:', token);
      console.log('Expected:', verifyToken);
      return res.status(HttpStatus.FORBIDDEN).json({
        error: 'Invalid verify token'
      });
    }

    console.log('✅ Webhook verified successfully');
    console.log('Sending challenge:', challenge);
    
    // Send challenge as plain text
    res.status(HttpStatus.OK).send(challenge);
  }

  @Post()
  async handleWebhook(@Body(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: false, // Permitir propiedades adicionales
    skipMissingProperties: true,
  })) body: any, @Res() res: Response) { // Cambiamos a 'any' para ser más flexible
    try {
      console.log('=== WEBHOOK RECEIVED ===');
      console.log('Body:', JSON.stringify(body, null, 2));

      // Validación básica manual
      if (!body || !body.object) {
        console.log('Invalid webhook body - missing object property');
        return res.sendStatus(HttpStatus.BAD_REQUEST);
      }

      if (body.object === 'whatsapp_business_account') {
        console.log('Processing WhatsApp message...');
        await this.processWhatsAppMessage(body);
      } else {
        console.log('Unrecognized object:', body.object);
      }

      res.sendStatus(HttpStatus.OK);
    } catch (error) {
      console.error('❌ Error in webhook:', error);
      res.sendStatus(HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('metrics')
  getMetrics(@Res() res: Response) {
    try {
      const metrics = this.aiProcessor.getMetrics();
      
      // Format for HTTP response
      const formattedMetrics = {
        ...metrics,
        successRate: metrics.totalRequests > 0 
          ? ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1) + '%'
          : '0%',
        averageProcessingTime: metrics.processingTimes.length > 0
          ? (metrics.processingTimes.reduce((a, b) => a + b, 0) / metrics.processingTimes.length).toFixed(0) + 'ms'
          : '0ms'
      };

      res.status(HttpStatus.OK).json({
        status: 'success',
        data: formattedMetrics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting metrics:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: 'error',
        message: 'Failed to get metrics'
      });
    }
  }

  private async processWhatsAppMessage(body: any): Promise<void> {
    try {
      if (!body.entry || !Array.isArray(body.entry)) {
        console.log('No entry array found in webhook body');
        return;
      }

      for (const entry of body.entry) {
        if (!entry.changes || !Array.isArray(entry.changes)) {
          console.log('No changes array found in entry');
          continue;
        }

        for (const change of entry.changes) {
          console.log('Processing change:', change.field);

          // Manejar diferentes tipos de cambios
          if (change.field === 'messages') {
            await this.handleMessagesChange(change);
          } else if (change.field === 'message_status') {
            console.log('Message status update received');
            // Aquí podrías manejar actualizaciones de estado de mensajes
          } else {
            console.log('Unhandled change field:', change.field);
          }
        }
      }
    } catch (error) {
      console.error('Error processing WhatsApp message:', error);
    }
  }

  private async handleMessagesChange(change: any): Promise<void> {
    try {
      if (!change.value) {
        console.log('No value found in messages change');
        return;
      }

      // Manejar mensajes
      if (change.value.messages && Array.isArray(change.value.messages)) {
        for (const message of change.value.messages) {
          await this.processIndividualMessage(message);
        }
      }

      // Manejar contactos (información del remitente)
      if (change.value.contacts && Array.isArray(change.value.contacts)) {
        console.log('Contact information received:', change.value.contacts);
      }

      // Manejar estados de mensajes
      if (change.value.statuses && Array.isArray(change.value.statuses)) {
        console.log('Message statuses received:', change.value.statuses);
      }
    } catch (error) {
      console.error('Error handling messages change:', error);
    }
  }

  private async processIndividualMessage(message: any): Promise<void> {
    try {
      if (!message || !message.from || !message.type) {
        console.log('Invalid message structure');
        return;
      }

      const phoneNumber = this.formatPhoneNumber(message.from);
      console.log('Phone number:', phoneNumber);
      console.log('Message type:', message.type);

      // Route to appropriate use case based on message type
      if (message.type === 'text' && message.text) {
        await this.processTextMessageUseCase.execute({
          text: message.text.body,
          phoneNumber,
          messageId: message.id,
          timestamp: message.timestamp,
        });
      } else if (message.type === 'audio' && message.audio) {
        await this.processAudioMessageUseCase.execute({
          audioId: message.audio.id,
          phoneNumber,
          messageId: message.id,
          timestamp: message.timestamp,
        });
      } else if (message.type === 'interactive' && message.interactive) {
        const buttonId = message.interactive.button_reply?.id ||
                        message.interactive.list_reply?.id || '';

        await this.processInteractiveMessageUseCase.execute({
          buttonId,
          phoneNumber,
          messageId: message.id,
          timestamp: message.timestamp,
        });
      } else {
        console.log('Unsupported message type:', message.type);
        // Log the message structure for debugging
        console.log('Message structure:', JSON.stringify(message, null, 2));
      }
    } catch (error) {
      console.error('Error processing individual message:', error);
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove +54 9 prefix and format for Argentina
    let formatted = phoneNumber.replace(/^\+?54\s?9?/, '');
    
    // Ensure it starts with 54
    if (!formatted.startsWith('54')) {
      formatted = '54' + formatted;
    }
    
    return formatted;
  }
}
