import { Controller, Get, Post, Body, Query, Res, HttpStatus, ValidationPipe } from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { ProcessTextMessageUseCase } from '../../application/use-cases/process-text-message.use-case';
import { ProcessAudioMessageUseCase } from '../../application/use-cases/process-audio-message.use-case';
import { ProcessInteractiveMessageUseCase } from '../../application/use-cases/process-interactive-message.use-case';
import { WebhookMessageDto } from '../dto/webhook-message.dto';
import { IAiProcessor } from '../../../ai/domain/interfaces/ai-processor.interface';

@ApiTags('webhook')
@Controller('webhook')
export class WebhookController {
  constructor(
    private readonly configService: ConfigService,
    private readonly processTextMessageUseCase: ProcessTextMessageUseCase,
    private readonly processAudioMessageUseCase: ProcessAudioMessageUseCase,
    private readonly processInteractiveMessageUseCase: ProcessInteractiveMessageUseCase,
    private readonly aiProcessor: IAiProcessor,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Verify webhook for WhatsApp Business API' })
  @ApiQuery({ name: 'hub.mode', required: true })
  @ApiQuery({ name: 'hub.verify_token', required: true })
  @ApiQuery({ name: 'hub.challenge', required: true })
  @ApiResponse({ status: 200, description: 'Webhook verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid verification parameters' })
  @ApiResponse({ status: 403, description: 'Invalid verify token' })
  verifyWebhook(@Query() query: any, @Res() res: Response) {
    console.log('=== WEBHOOK VERIFICATION ===');
    console.log('Query received:', JSON.stringify(query, null, 2));
    
    const mode = query['hub.mode'];
    const token = query['hub.verify_token'];
    const challenge = query['hub.challenge'];

    console.log('Extracted parameters:');
    console.log('- Mode:', mode);
    console.log('- Token received:', token);
    console.log('- Challenge:', challenge);

    const verifyToken = this.configService.get<string>('VERIFY_TOKEN');
    console.log('- Expected token:', verifyToken);

    // Check that all required parameters are present
    if (!mode || !token || !challenge) {
      console.log('❌ Missing required parameters');
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Missing required parameters',
        received: { 
          mode, 
          token: token ? 'present' : 'missing', 
          challenge: challenge ? 'present' : 'missing' 
        }
      });
    }

    // Check that mode is correct
    if (mode !== 'subscribe') {
      console.log('❌ Invalid mode:', mode);
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Invalid mode',
        received: mode,
        expected: 'subscribe'
      });
    }

    // Verify token
    if (token !== verifyToken) {
      console.log('❌ Token mismatch');
      console.log('Received:', token);
      console.log('Expected:', verifyToken);
      return res.status(HttpStatus.FORBIDDEN).json({
        error: 'Invalid verify token'
      });
    }

    console.log('✅ Webhook verified successfully');
    console.log('Sending challenge:', challenge);
    
    // Send challenge as plain text
    res.status(HttpStatus.OK).send(challenge);
  }

  @Post()
  @ApiOperation({ summary: 'Handle incoming WhatsApp messages' })
  @ApiResponse({ status: 200, description: 'Message processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid message format' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async handleWebhook(@Body(ValidationPipe) body: WebhookMessageDto, @Res() res: Response) {
    try {
      console.log('=== WEBHOOK RECEIVED ===');
      console.log('Body:', JSON.stringify(body, null, 2));

      if (body.object === 'whatsapp_business_account') {
        console.log('Processing WhatsApp message...');
        await this.processWhatsAppMessage(body);
      } else {
        console.log('Unrecognized object:', body.object);
      }

      res.sendStatus(HttpStatus.OK);
    } catch (error) {
      console.error('❌ Error in webhook:', error);
      res.sendStatus(HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Get AI processing metrics' })
  @ApiResponse({ status: 200, description: 'Metrics retrieved successfully' })
  getMetrics(@Res() res: Response) {
    try {
      const metrics = this.aiProcessor.getMetrics();
      
      // Format for HTTP response
      const formattedMetrics = {
        ...metrics,
        successRate: metrics.totalRequests > 0 
          ? ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1) + '%'
          : '0%',
        averageProcessingTime: metrics.processingTimes.length > 0
          ? (metrics.processingTimes.reduce((a, b) => a + b, 0) / metrics.processingTimes.length).toFixed(0) + 'ms'
          : '0ms'
      };

      res.status(HttpStatus.OK).json({
        status: 'success',
        data: formattedMetrics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting metrics:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: 'error',
        message: 'Failed to get metrics'
      });
    }
  }

  private async processWhatsAppMessage(body: WebhookMessageDto): Promise<void> {
    for (const entry of body.entry) {
      for (const change of entry.changes) {
        if (change.field !== 'messages') continue;

        const message = change.value.messages[0];
        const phoneNumber = this.formatPhoneNumber(message.from);

        console.log('Phone number:', phoneNumber);
        console.log('Message type:', message.type);

        // Route to appropriate use case based on message type
        if (message.type === 'text' && message.text) {
          await this.processTextMessageUseCase.execute({
            text: message.text.body,
            phoneNumber,
            messageId: message.id,
            timestamp: message.timestamp,
          });
        } else if (message.type === 'audio' && message.audio) {
          await this.processAudioMessageUseCase.execute({
            audioId: message.audio.id,
            phoneNumber,
            messageId: message.id,
            timestamp: message.timestamp,
          });
        } else if (message.type === 'interactive' && message.interactive) {
          const buttonId = message.interactive.button_reply?.id || 
                          message.interactive.list_reply?.id || '';
          
          await this.processInteractiveMessageUseCase.execute({
            buttonId,
            phoneNumber,
            messageId: message.id,
            timestamp: message.timestamp,
          });
        } else {
          console.log('Unsupported message type:', message.type);
          // Could add a use case for unsupported messages if needed
        }
      }
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove +54 9 prefix and format for Argentina
    let formatted = phoneNumber.replace(/^\+?54\s?9?/, '');
    
    // Ensure it starts with 54
    if (!formatted.startsWith('54')) {
      formatted = '54' + formatted;
    }
    
    return formatted;
  }
}
