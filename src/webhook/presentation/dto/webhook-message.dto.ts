import { IsString, IsObject, IsOptional, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export class AudioMessageDto {
  @IsString()
  id: string;

  @IsOptional()
  @IsString()
  mime_type?: string;
}

export class TextMessageDto {
  @IsString()
  body: string;
}

export class InteractiveMessageDto {
  @IsString()
  type: string;

  @IsObject()
  button_reply?: {
    id: string;
    title: string;
  };

  @IsObject()
  list_reply?: {
    id: string;
    title: string;
    description?: string;
  };
}

export class MessageDto {
  @IsString()
  id: string;

  @IsString()
  from: string;

  @IsString()
  timestamp: string;

  @IsString()
  type: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => TextMessageDto)
  text?: TextMessageDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AudioMessageDto)
  audio?: AudioMessageDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => InteractiveMessageDto)
  interactive?: InteractiveMessageDto;
}

export class ValueDto {
  @IsString()
  messaging_product: string;

  @IsObject()
  metadata: {
    display_phone_number: string;
    phone_number_id: string;
  };

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageDto)
  messages: MessageDto[];
}

export class ChangeDto {
  @IsString()
  field: string;

  @IsValidateNested()
  @Type(() => ValueDto)
  value: ValueDto;
}

export class EntryDto {
  @IsString()
  id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChangeDto)
  changes: ChangeDto[];
}

export class WebhookMessageDto {
  @IsString()
  object: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EntryDto)
  entry: EntryDto[];
}
