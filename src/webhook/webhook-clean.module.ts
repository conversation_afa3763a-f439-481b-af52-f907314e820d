import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Import clean modules
import { ExpenseCleanModule } from '../expense/expense-clean.module';
import { AiCleanModule } from '../ai/ai-clean.module';

// Application use cases
import { ProcessTextMessageUseCase } from './application/use-cases/process-text-message.use-case';
import { ProcessAudioMessageUseCase } from './application/use-cases/process-audio-message.use-case';
import { ProcessInteractiveMessageUseCase } from './application/use-cases/process-interactive-message.use-case';

// Presentation
import { WebhookController } from './presentation/controllers/webhook.controller';

@Module({
  imports: [
    ConfigModule,
    ExpenseCleanModule,
    AiCleanModule,
  ],
  controllers: [WebhookController],
  providers: [
    ProcessTextMessageUseCase,
    ProcessAudioMessageUseCase,
    ProcessInteractiveMessageUseCase,
  ],
  exports: [
    ProcessTextMessageUseCase,
    ProcessAudioMessageUseCase,
    ProcessInteractiveMessageUseCase,
  ],
})
export class WebhookCleanModule {}
