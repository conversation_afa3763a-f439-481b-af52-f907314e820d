import { Injectable } from '@nestjs/common';
import { WhatsAppService } from '../expense/whatsapp.service';
import { ExpenseBusinessService } from '../expense/expense-business.service';
import { GeminiService } from '../ai/gemini.service';
import { Expense } from '../expense/interfaces/expense.interface';

@Injectable()
export class WebhookService {
  constructor(
    private readonly whatsappService: WhatsAppService,
    private readonly expenseBusinessService: ExpenseBusinessService,
    private readonly geminiService: GeminiService,
  ) {}

  async processWhatsAppMessage(body: any): Promise<void> {
    try {
      console.log('=== PROCESANDO MENSAJE DE WHATSAPP ===');
      console.log('Body completo:', JSON.stringify(body, null, 2));

      if (!body.entry || !body.entry[0] || !body.entry[0].changes || !body.entry[0].changes[0]) {
        console.log('Estructura de mensaje inválida');
        return;
      }

      const change = body.entry[0].changes[0];
      if (!change.value || !change.value.messages || change.value.messages.length === 0) {
        console.log('No hay mensajes para procesar');
        return;
      }

      const message = change.value.messages[0];
      const phoneNumber = this.formatPhoneNumber(message.from);

      console.log('Número de teléfono:', phoneNumber);
      console.log('Tipo de mensaje:', message.type);

      // Manejar diferentes tipos de mensaje
      if (message.type === 'text') {
        await this.handleTextMessage(message, phoneNumber);
      } else if (message.type === 'audio') {
        await this.handleAudioMessage(message, phoneNumber);
      } else if (message.type === 'interactive') {
        await this.handleInteractiveMessage(message, phoneNumber);
      } else {
        console.log('Tipo de mensaje no soportado:', message.type);
        await this.whatsappService.sendMessage(
          phoneNumber,
          'Lo siento, solo puedo procesar mensajes de texto y audio por ahora. 🎤📝'
        );
      }

    } catch (error) {
      console.error('❌ Error procesando mensaje de WhatsApp:', error);
    }
  }

  private async handleTextMessage(message: any, phoneNumber: string): Promise<void> {
    const messageText = message.text.body;
    console.log('Mensaje de texto:', messageText);

    // Detectar si es un mensaje estructurado o lenguaje natural
    if (this.isStructuredMessage(messageText)) {
      console.log('Procesando como mensaje estructurado...');
      await this.expenseBusinessService.processStructuredMessage(messageText, phoneNumber);
    } else {
      console.log('Procesando como lenguaje natural...');
      const result = await this.geminiService.processNaturalLanguage(messageText);
      
      if (result.success && result.data) {
        await this.expenseBusinessService.processGeminiResult(result.data, phoneNumber);
      } else {
        await this.whatsappService.sendMessage(
          phoneNumber,
          `No pude entender tu mensaje. ${result.error || 'Intenta ser más específico sobre el gasto.'} 🤔`
        );
      }
    }
  }

  private async handleAudioMessage(message: any, phoneNumber: string): Promise<void> {
    try {
      console.log('=== PROCESANDO MENSAJE DE AUDIO ===');
      console.log('Audio message:', message.audio);

      if (!message.audio || !message.audio.id) {
        console.error('Mensaje de audio inválido');
        await this.whatsappService.sendMessage(
          phoneNumber,
          'No pude procesar el audio. Intenta enviarlo de nuevo. 🎤'
        );
        return;
      }

      // Enviar mensaje de "procesando"
      await this.whatsappService.sendMessage(
        phoneNumber,
        'Escuchando tu audio... 🎧 Dame un momento para procesarlo.'
      );

      // Descargar el audio de WhatsApp
      const audioData = await this.whatsappService.downloadAudio(message.audio.id);
      
      if (!audioData) {
        console.error('No se pudo descargar el audio');
        await this.whatsappService.sendMessage(
          phoneNumber,
          'No pude descargar el audio. Intenta enviarlo de nuevo. 🎤'
        );
        return;
      }

      // Convertir audio para Gemini si es necesario
      const geminiAudio = await this.whatsappService.convertAudioForGemini(
        audioData.buffer, 
        audioData.mimeType
      );

      // Procesar audio con Gemini
      const result = await this.geminiService.processAudioMessage(
        geminiAudio.buffer, 
        geminiAudio.mimeType
      );

      if (result.success && result.data) {
        console.log('✅ Audio procesado exitosamente');
        console.log('Transcripción:', result.transcription);
        
        // Enviar transcripción al usuario
        if (result.transcription) {
          await this.whatsappService.sendMessage(
            phoneNumber,
            `📝 Escuché: "${result.transcription}"`
          );
        }

        // Procesar el gasto extraído
        await this.expenseBusinessService.processGeminiResult(result.data, phoneNumber, result.transcription);
      } else {
        console.error('Error procesando audio:', result.error);
        
        let errorMessage = 'No pude entender el audio. ';
        
        if (result.transcription) {
          errorMessage += `Escuché: "${result.transcription}". `;
        }
        
        errorMessage += 'Intenta hablar más claro o envía un mensaje de texto. 🎤➡️📝';
        
        await this.whatsappService.sendMessage(phoneNumber, errorMessage);
      }

    } catch (error) {
      console.error('❌ Error procesando audio:', error);
      await this.whatsappService.sendMessage(
        phoneNumber,
        'Hubo un error procesando tu audio. Intenta de nuevo o envía un mensaje de texto. 🎤❌'
      );
    }
  }

  private async handleInteractiveMessage(message: any, phoneNumber: string): Promise<void> {
    if (message.interactive && message.interactive.button_reply) {
      const buttonId = message.interactive.button_reply.id;
      console.log('Botón presionado:', buttonId);
      await this.expenseBusinessService.handleButtonResponse(buttonId, phoneNumber);
    }
  }

  private isStructuredMessage(text: string): boolean {
    // Detectar patrones como "1500 pesos coca cola" o "2000 supermercado"
    const structuredPattern = /^\d+\s*(pesos?|$)\s*\w+/i;
    return structuredPattern.test(text.trim());
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remover el prefijo +54 9 y formatear para Argentina
    let formatted = phoneNumber.replace(/^\+?54\s?9?/, '');
    
    // Asegurar que empiece con 54
    if (!formatted.startsWith('54')) {
      formatted = '54' + formatted;
    }
    
    return formatted;
  }
} 