import { Controller, Get, Post, Body, Query, Res, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { WebhookService } from './webhook.service';
import { ConfigService } from '@nestjs/config';
import { GeminiService } from '../ai/gemini.service';

@Controller('webhook')
export class WebhookController {
  constructor(
    private readonly webhookService: WebhookService,
    private readonly configService: ConfigService,
    private readonly geminiService: GeminiService,
  ) {}

  @Get()
  verifyWebhook(@Query() query: any, @Res() res: Response) {
    console.log('=== VERIFICACIÓN DE WEBHOOK ===');
    console.log('Query recibidos:', JSON.stringify(query, null, 2));
    
    const mode = query['hub.mode'];
    const token = query['hub.verify_token'];
    const challenge = query['hub.challenge'];

    console.log('Parámetros extraídos:');
    console.log('- Mode:', mode);
    console.log('- Token recibido:', token);
    console.log('- Challenge:', challenge);

    const verifyToken = this.configService.get<string>('VERIFY_TOKEN');
    console.log('- Token esperado:', verifyToken);

    // Verificar que todos los parámetros están presentes
    if (!mode || !token || !challenge) {
      console.log('❌ Faltan parámetros requeridos');
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Missing required parameters',
        received: { mode, token: token ? 'present' : 'missing', challenge: challenge ? 'present' : 'missing' }
      });
    }

    // Verificar que el modo es correcto
    if (mode !== 'subscribe') {
      console.log('❌ Modo incorrecto:', mode);
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Invalid mode',
        received: mode,
        expected: 'subscribe'
      });
    }

    // Verificar el token
    if (token !== verifyToken) {
      console.log('❌ Token no coincide');
      console.log('Recibido:', token);
      console.log('Esperado:', verifyToken);
      return res.status(HttpStatus.FORBIDDEN).json({
        error: 'Invalid verify token'
      });
    }

    console.log('✅ Webhook verificado exitosamente');
    console.log('Enviando challenge:', challenge);
    
    // Enviar el challenge como texto plano
    res.status(HttpStatus.OK).send(challenge);
  }

  @Post()
  async handleWebhook(@Body() body: any, @Res() res: Response) {
    try {
      console.log('=== WEBHOOK RECIBIDO ===');
      console.log('Body completo:', JSON.stringify(body, null, 2));

      if (body.object === 'whatsapp_business_account') {
        console.log('Procesando mensaje de WhatsApp...');
        await this.webhookService.processWhatsAppMessage(body);
      } else {
        console.log('Objeto no reconocido:', body.object);
      }

      res.sendStatus(HttpStatus.OK);
    } catch (error) {
      console.error('❌ Error en webhook:', error);
      res.sendStatus(HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('metrics')
  getMetrics(@Res() res: Response) {
    try {
      const metrics = this.geminiService.getMetrics();
      
      // Formatear para respuesta HTTP
      const formattedMetrics = {
        ...metrics,
        successRate: metrics.totalRequests > 0 
          ? ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1) + '%'
          : '0%',
        averageProcessingTime: metrics.processingTimes.length > 0
          ? (metrics.processingTimes.reduce((a, b) => a + b, 0) / metrics.processingTimes.length).toFixed(0) + 'ms'
          : '0ms'
      };

      res.status(HttpStatus.OK).json({
        status: 'success',
        data: formattedMetrics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error obteniendo métricas:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: 'error',
        message: 'Error al obtener métricas'
      });
    }
  }
} 