import { Injectable } from '@nestjs/common';
import { ProcessAudioMessageUseCase as AiProcessAudioUseCase } from '../../../ai/application/use-cases/process-audio-message.use-case';
import { CreateExpenseUseCase } from '../../../expense/application/use-cases/create-expense.use-case';
import { WhatsAppService } from '../../../expense/infrastructure/external/whatsapp/whatsapp.service';

export interface AudioMessageData {
  audioId: string;
  phoneNumber: string;
  messageId: string;
  timestamp: string;
}

@Injectable()
export class ProcessAudioMessageUseCase {
  constructor(
    private readonly aiProcessAudioUseCase: AiProcessAudioUseCase,
    private readonly createExpenseUseCase: CreateExpenseUseCase,
    private readonly whatsappService: WhatsAppService,
  ) {}

  async execute(messageData: AudioMessageData): Promise<void> {
    try {
      console.log('=== PROCESSING AUDIO MESSAGE ===');
      console.log('Phone:', messageData.phoneNumber);
      console.log('Audio ID:', messageData.audioId);

      // Download audio from WhatsApp
      const audioData = await this.whatsappService.downloadAudio(messageData.audioId);
      
      if (!audioData) {
        console.error('Failed to download audio');
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          'No pude descargar el audio. Intenta enviarlo de nuevo. 🎤'
        );
        return;
      }

      // Convert audio for AI processing if needed
      const convertedAudio = await this.whatsappService.convertAudioForGemini(
        audioData.buffer, 
        audioData.mimeType
      );

      // Process audio with AI
      const result = await this.aiProcessAudioUseCase.execute(
        convertedAudio.buffer, 
        convertedAudio.mimeType
      );

      if (result.success && result.data) {
        console.log('✅ Audio processed successfully');
        console.log('Transcription:', result.transcription);
        
        // Send transcription to user
        if (result.transcription) {
          await this.whatsappService.sendMessage(
            messageData.phoneNumber,
            `📝 Escuché: "${result.transcription}"`
          );
        }

        // Check confidence level
        if (result.data.confidence < 0.6) {
          console.log('Low confidence, requesting clarification');
          await this.requestClarification(result, messageData.phoneNumber);
          return;
        }

        // Create expense
        try {
          const expenseDto = {
            amount: result.data.amount,
            category: result.data.category,
            description: result.data.description,
            phoneNumber: messageData.phoneNumber,
          };

          const expense = await this.createExpenseUseCase.execute(expenseDto);
          
          await this.whatsappService.sendMessage(
            messageData.phoneNumber,
            `✅ Gasto registrado:\n💰 $${expense.amount}\n📂 ${expense.category}\n📝 ${expense.description}`
          );
        } catch (error) {
          console.error('Error creating expense from audio:', error);
          await this.whatsappService.sendMessage(
            messageData.phoneNumber,
            'Error al registrar el gasto. Intenta de nuevo.'
          );
        }
      } else {
        console.error('Error processing audio:', result.error);
        
        let errorMessage = 'No pude entender el audio. ';
        
        if (result.transcription) {
          errorMessage += `Escuché: "${result.transcription}". `;
        }
        
        errorMessage += 'Intenta hablar más claro o envía un mensaje de texto. 🎤';
        
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          errorMessage
        );
      }
    } catch (error) {
      console.error('Error processing audio message:', error);
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        'Hubo un error procesando el audio. Intenta de nuevo. 🎤'
      );
    }
  }

  private async requestClarification(result: any, phoneNumber: string): Promise<void> {
    let message = 'No estoy completamente seguro de lo que dijiste. ';
    
    if (result.transcription) {
      message += `Escuché: "${result.transcription}". `;
    }
    
    if (result.data && result.data.amount > 0) {
      message += `¿Te refieres a un gasto de $${result.data.amount}`;
      
      if (result.data.category && result.data.category !== 'Otros') {
        message += ` en ${result.data.category.toLowerCase()}`;
      }
      
      message += '? ';
    }
    
    message += 'Puedes confirmar o enviar un mensaje de texto con los detalles. 🤔';
    
    await this.whatsappService.sendMessage(phoneNumber, message);
  }
}
