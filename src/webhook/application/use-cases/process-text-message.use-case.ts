import { Injectable } from '@nestjs/common';
import { ProcessNaturalLanguageUseCase } from '../../../ai/application/use-cases/process-natural-language.use-case';
import { CreateExpenseUseCase } from '../../../expense/application/use-cases/create-expense.use-case';
import { WhatsAppService } from '../../../expense/infrastructure/external/whatsapp/whatsapp.service';

export interface TextMessageData {
  text: string;
  phoneNumber: string;
  messageId: string;
  timestamp: string;
}

@Injectable()
export class ProcessTextMessageUseCase {
  constructor(
    private readonly processNaturalLanguageUseCase: ProcessNaturalLanguageUseCase,
    private readonly createExpenseUseCase: CreateExpenseUseCase,
    private readonly whatsappService: WhatsAppService,
  ) {}

  async execute(messageData: TextMessageData): Promise<void> {
    try {
      console.log('=== PROCESSING TEXT MESSAGE ===');
      console.log('Phone:', messageData.phoneNumber);
      console.log('Text:', messageData.text);

      // Check if it's a structured message or natural language
      if (this.isStructuredMessage(messageData.text)) {
        await this.processStructuredMessage(messageData);
      } else {
        await this.processNaturalLanguageMessage(messageData);
      }
    } catch (error) {
      console.error('Error processing text message:', error);
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        'Lo siento, hubo un error procesando tu mensaje. Intenta de nuevo. 🤔'
      );
    }
  }

  private async processStructuredMessage(messageData: TextMessageData): Promise<void> {
    console.log('Processing as structured message...');
    
    const expenseMatch = this.parseExpenseFromText(messageData.text);
    if (expenseMatch) {
      try {
        const expenseDto = {
          amount: expenseMatch.amount,
          category: expenseMatch.category || 'Otros',
          description: expenseMatch.description,
          phoneNumber: messageData.phoneNumber,
        };

        const expense = await this.createExpenseUseCase.execute(expenseDto);
        
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          `✅ Gasto registrado:\n💰 $${expense.amount}\n📂 ${expense.category}\n📝 ${expense.description}`
        );
      } catch (error) {
        console.error('Error creating expense from structured message:', error);
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          'Error al registrar el gasto. Verifica el formato e intenta de nuevo.'
        );
      }
    } else {
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        'No pude entender el formato del gasto. Intenta con: "monto descripción" (ej: "2500 almuerzo")'
      );
    }
  }

  private async processNaturalLanguageMessage(messageData: TextMessageData): Promise<void> {
    console.log('Processing as natural language...');
    
    const result = await this.processNaturalLanguageUseCase.execute(messageData.text);
    
    if (result.success && result.data) {
      if (result.data.confidence < 0.6) {
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          `No estoy seguro de haber entendido bien. ¿Te refieres a un gasto de $${result.data.amount} en ${result.data.category.toLowerCase()}? 🤔`
        );
        return;
      }

      try {
        const expenseDto = {
          amount: result.data.amount,
          category: result.data.category,
          description: result.data.description,
          phoneNumber: messageData.phoneNumber,
        };

        const expense = await this.createExpenseUseCase.execute(expenseDto);
        
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          `✅ Gasto registrado:\n💰 $${expense.amount}\n📂 ${expense.category}\n📝 ${expense.description}`
        );
      } catch (error) {
        console.error('Error creating expense from AI result:', error);
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          'Error al registrar el gasto. Intenta de nuevo.'
        );
      }
    } else {
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        `No pude entender tu mensaje. ${result.error || 'Intenta ser más específico sobre el gasto.'} 🤔`
      );
    }
  }

  private isStructuredMessage(text: string): boolean {
    // Detect patterns like "1500 pesos coca cola" or "2000 supermercado"
    const structuredPattern = /^\d+\s*(pesos?|$)\s*\w+/i;
    return structuredPattern.test(text.trim());
  }

  private parseExpenseFromText(text: string): { amount: number; category?: string; description: string } | null {
    // Simple regex to extract amount and description
    const match = text.match(/(\d+(?:\.\d{2})?)\s*(?:pesos?)?\s*(.+)/i);
    
    if (match) {
      const amount = parseFloat(match[1]);
      const description = match[2].trim();
      
      return {
        amount,
        description,
        category: this.categorizeExpense(description)
      };
    }
    
    return null;
  }

  private categorizeExpense(description: string): string {
    const desc = description.toLowerCase();
    
    // Food categories
    if (desc.includes('coca') || desc.includes('comida') || desc.includes('almuerzo') || 
        desc.includes('cena') || desc.includes('desayuno') || desc.includes('restaurant')) {
      return 'Comida';
    }
    
    // Transport
    if (desc.includes('uber') || desc.includes('taxi') || desc.includes('colectivo') || 
        desc.includes('subte') || desc.includes('transporte')) {
      return 'Transporte';
    }
    
    // Supermarket
    if (desc.includes('super') || desc.includes('chino') || desc.includes('verdulería') ||
        desc.includes('carnicería') || desc.includes('panadería')) {
      return 'Supermercado';
    }
    
    // Services
    if (desc.includes('luz') || desc.includes('gas') || desc.includes('agua') || 
        desc.includes('internet') || desc.includes('telefono')) {
      return 'Servicios';
    }
    
    return 'Otros';
  }
}
