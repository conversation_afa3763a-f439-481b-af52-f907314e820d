import { Injectable } from '@nestjs/common';
import { CreateExpenseUseCase } from '../../../expense/application/use-cases/create-expense.use-case';
import { WhatsAppService } from '../../../expense/infrastructure/external/whatsapp/whatsapp.service';

export interface InteractiveMessageData {
  buttonId: string;
  phoneNumber: string;
  messageId: string;
  timestamp: string;
}

@Injectable()
export class ProcessInteractiveMessageUseCase {
  constructor(
    private readonly createExpenseUseCase: CreateExpenseUseCase,
    private readonly whatsappService: WhatsAppService,
  ) {}

  async execute(messageData: InteractiveMessageData): Promise<void> {
    try {
      console.log('=== PROCESSING INTERACTIVE MESSAGE ===');
      console.log('Phone:', messageData.phoneNumber);
      console.log('Button ID:', messageData.buttonId);

      if (messageData.buttonId.startsWith('confirm_')) {
        await this.handleConfirmation(messageData);
      } else if (messageData.buttonId.startsWith('cancel_')) {
        await this.handleCancellation(messageData);
      } else {
        console.log('Unknown button interaction:', messageData.buttonId);
        await this.whatsappService.sendMessage(
          messageData.phoneNumber,
          'No reconozco esa opción. Intenta de nuevo. 🤔'
        );
      }
    } catch (error) {
      console.error('Error processing interactive message:', error);
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        'Hubo un error procesando tu respuesta. Intenta de nuevo.'
      );
    }
  }

  private async handleConfirmation(messageData: InteractiveMessageData): Promise<void> {
    const pendingId = messageData.buttonId.replace('confirm_', '');
    const pendingExpense = this.whatsappService.getPendingExpense(pendingId);

    if (!pendingExpense) {
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        'No encontré el gasto pendiente. Es posible que haya expirado. Intenta registrarlo de nuevo.'
      );
      return;
    }

    try {
      // Convert domain expense to DTO
      const expenseDto = {
        amount: pendingExpense.amount.amount,
        category: pendingExpense.category.value,
        description: pendingExpense.description,
        phoneNumber: pendingExpense.phoneNumber.value,
        installment_details: pendingExpense.installmentDetails ? {
          group_id: pendingExpense.installmentDetails.groupId,
          current_installment: pendingExpense.installmentDetails.currentInstallment,
          total_installments: pendingExpense.installmentDetails.totalInstallments,
        } : undefined,
      };

      const savedExpense = await this.createExpenseUseCase.execute(expenseDto);

      // Remove from pending
      this.whatsappService.removePendingExpense(pendingId);

      // Send confirmation message in the expected format
      let confirmationMessage = `✅ Gasto guardado exitosamente:\n`;
      confirmationMessage += `💰 $${savedExpense.amount}\n`;
      confirmationMessage += `📁 ${savedExpense.category}\n`;
      confirmationMessage += `📝 ${savedExpense.description}`;

      if (savedExpense.installment_details) {
        confirmationMessage += `\n📊 Cuota: ${savedExpense.installment_details.current_installment}/${savedExpense.installment_details.total_installments}`;
      }

      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        confirmationMessage
      );

    } catch (error) {
      console.error('Error saving confirmed expense:', error);
      
      // Remove from pending even if save failed
      this.whatsappService.removePendingExpense(pendingId);
      
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        'Hubo un error guardando el gasto. Intenta registrarlo de nuevo.'
      );
    }
  }

  private async handleCancellation(messageData: InteractiveMessageData): Promise<void> {
    const pendingId = messageData.buttonId.replace('cancel_', '');
    const pendingExpense = this.whatsappService.getPendingExpense(pendingId);

    if (!pendingExpense) {
      await this.whatsappService.sendMessage(
        messageData.phoneNumber,
        'No encontré el gasto pendiente para cancelar.'
      );
      return;
    }

    // Remove from pending
    this.whatsappService.removePendingExpense(pendingId);

    await this.whatsappService.sendMessage(
      messageData.phoneNumber,
      '❌ Gasto cancelado. No se guardó ningún registro.\n\nPuedes enviar otro mensaje para registrar un nuevo gasto.'
    );
  }
}
