export class DomainException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DomainException';
  }
}

export class InvalidExpenseException extends DomainException {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidExpenseException';
  }
}

export class InvalidPhoneNumberException extends DomainException {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidPhoneNumberException';
  }
}

export class InvalidCategoryException extends DomainException {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidCategoryException';
  }
}

export class InvalidAmountException extends DomainException {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidAmountException';
  }
}

export class ExpenseNotFoundException extends DomainException {
  constructor(id: string) {
    super(`Expense with ID ${id} not found`);
    this.name = 'ExpenseNotFoundException';
  }
}
