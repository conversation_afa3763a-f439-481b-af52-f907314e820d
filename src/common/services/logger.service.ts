import { Injectable, LoggerService as NestLoggerService, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

export interface LogContext {
  userId?: string;
  phoneNumber?: string;
  requestId?: string;
  operation?: string;
  duration?: number;
  [key: string]: any;
}

@Injectable()
export class AppLoggerService implements NestLoggerService {
  private context?: string;
  private logLevel: LogLevel;

  constructor(private configService: ConfigService) {
    const level = this.configService.get<string>('LOG_LEVEL', 'info');
    this.logLevel = this.getLogLevel(level);
  }

  setContext(context: string): void {
    this.context = context;
  }

  log(message: string, context?: LogContext): void {
    this.info(message, context);
  }

  error(message: string, trace?: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.ERROR) {
      this.writeLog('ERROR', message, { ...context, trace });
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.WARN) {
      this.writeLog('WARN', message, context);
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.INFO) {
      this.writeLog('INFO', message, context);
    }
  }

  debug(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.DEBUG) {
      this.writeLog('DEBUG', message, context);
    }
  }

  verbose(message: string, context?: LogContext): void {
    this.debug(message, context);
  }

  // Business-specific logging methods
  logExpenseCreated(expenseId: string, amount: number, phoneNumber: string): void {
    this.info('Expense created', {
      operation: 'CREATE_EXPENSE',
      expenseId,
      amount,
      phoneNumber,
    });
  }

  logAiProcessing(
    inputType: 'text' | 'audio',
    processingTime: number,
    success: boolean,
    confidence?: number,
  ): void {
    this.info('AI processing completed', {
      operation: 'AI_PROCESSING',
      inputType,
      duration: processingTime,
      success,
      confidence,
    });
  }

  logWebhookReceived(messageType: string, phoneNumber: string): void {
    this.info('Webhook message received', {
      operation: 'WEBHOOK_RECEIVED',
      messageType,
      phoneNumber,
    });
  }

  logWhatsAppMessage(phoneNumber: string, messageType: 'sent' | 'received'): void {
    this.info('WhatsApp message', {
      operation: 'WHATSAPP_MESSAGE',
      phoneNumber,
      messageType,
    });
  }

  logRepositoryOperation(
    operation: string,
    entityType: string,
    entityId?: string,
    duration?: number,
  ): void {
    this.debug('Repository operation', {
      operation: `REPO_${operation.toUpperCase()}`,
      entityType,
      entityId,
      duration,
    });
  }

  private writeLog(level: string, message: string, context?: LogContext): void {
    const timestamp = new Date().toISOString();
    const logContext = this.context || 'Application';
    
    const logEntry = {
      timestamp,
      level,
      context: logContext,
      message,
      ...context,
    };

    // In production, you might want to use a structured logging library
    // like Winston or send logs to an external service
    if (this.configService.get('NODE_ENV') === 'production') {
      console.log(JSON.stringify(logEntry));
    } else {
      // Pretty print for development
      const contextStr = context ? ` ${JSON.stringify(context)}` : '';
      console.log(`[${timestamp}] [${level}] [${logContext}] ${message}${contextStr}`);
    }
  }

  private getLogLevel(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'error':
        return LogLevel.ERROR;
      case 'warn':
        return LogLevel.WARN;
      case 'info':
        return LogLevel.INFO;
      case 'debug':
        return LogLevel.DEBUG;
      default:
        return LogLevel.INFO;
    }
  }
}
