import { Injectable } from '@nestjs/common';

export interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
}

export interface CounterMetric {
  count: number;
  lastUpdated: Date;
}

export interface TimerMetric {
  count: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  lastUpdated: Date;
}

export interface ApplicationMetrics {
  counters: Record<string, CounterMetric>;
  timers: Record<string, TimerMetric>;
  gauges: Record<string, number>;
  lastReset: Date;
}

@Injectable()
export class MetricsService {
  private metrics: ApplicationMetrics = {
    counters: {},
    timers: {},
    gauges: {},
    lastReset: new Date(),
  };

  // Counter methods
  incrementCounter(name: string, value: number = 1, tags?: Record<string, string>): void {
    const key = this.buildKey(name, tags);
    
    if (!this.metrics.counters[key]) {
      this.metrics.counters[key] = {
        count: 0,
        lastUpdated: new Date(),
      };
    }
    
    this.metrics.counters[key].count += value;
    this.metrics.counters[key].lastUpdated = new Date();
  }

  getCounter(name: string, tags?: Record<string, string>): CounterMetric | undefined {
    const key = this.buildKey(name, tags);
    return this.metrics.counters[key];
  }

  // Timer methods
  recordTime(name: string, timeMs: number, tags?: Record<string, string>): void {
    const key = this.buildKey(name, tags);
    
    if (!this.metrics.timers[key]) {
      this.metrics.timers[key] = {
        count: 0,
        totalTime: 0,
        averageTime: 0,
        minTime: timeMs,
        maxTime: timeMs,
        lastUpdated: new Date(),
      };
    }
    
    const timer = this.metrics.timers[key];
    timer.count++;
    timer.totalTime += timeMs;
    timer.averageTime = timer.totalTime / timer.count;
    timer.minTime = Math.min(timer.minTime, timeMs);
    timer.maxTime = Math.max(timer.maxTime, timeMs);
    timer.lastUpdated = new Date();
  }

  startTimer(name: string, tags?: Record<string, string>): () => void {
    const startTime = Date.now();
    
    return () => {
      const duration = Date.now() - startTime;
      this.recordTime(name, duration, tags);
    };
  }

  getTimer(name: string, tags?: Record<string, string>): TimerMetric | undefined {
    const key = this.buildKey(name, tags);
    return this.metrics.timers[key];
  }

  // Gauge methods
  setGauge(name: string, value: number, tags?: Record<string, string>): void {
    const key = this.buildKey(name, tags);
    this.metrics.gauges[key] = value;
  }

  getGauge(name: string, tags?: Record<string, string>): number | undefined {
    const key = this.buildKey(name, tags);
    return this.metrics.gauges[key];
  }

  // Business-specific metrics
  recordExpenseCreated(category: string, amount: number): void {
    this.incrementCounter('expenses.created', 1, { category });
    this.setGauge('expenses.last_amount', amount, { category });
  }

  recordAiProcessing(type: 'text' | 'audio', duration: number, success: boolean): void {
    this.incrementCounter('ai.requests', 1, { type, success: success.toString() });
    this.recordTime('ai.processing_time', duration, { type });
  }

  recordWebhookMessage(messageType: string): void {
    this.incrementCounter('webhook.messages', 1, { type: messageType });
  }

  recordWhatsAppMessage(direction: 'sent' | 'received'): void {
    this.incrementCounter('whatsapp.messages', 1, { direction });
  }

  recordRepositoryOperation(operation: string, entityType: string, duration: number): void {
    this.incrementCounter('repository.operations', 1, { operation, entityType });
    this.recordTime('repository.operation_time', duration, { operation, entityType });
  }

  // Utility methods
  getAllMetrics(): ApplicationMetrics {
    return { ...this.metrics };
  }

  getMetricsSummary(): {
    totalExpenses: number;
    totalAiRequests: number;
    totalWebhookMessages: number;
    averageAiProcessingTime: number;
    successfulAiRequests: number;
    failedAiRequests: number;
  } {
    const totalExpenses = Object.entries(this.metrics.counters)
      .filter(([key]) => key.startsWith('expenses.created'))
      .reduce((sum, [, metric]) => sum + metric.count, 0);

    const totalAiRequests = Object.entries(this.metrics.counters)
      .filter(([key]) => key.startsWith('ai.requests'))
      .reduce((sum, [, metric]) => sum + metric.count, 0);

    const totalWebhookMessages = Object.entries(this.metrics.counters)
      .filter(([key]) => key.startsWith('webhook.messages'))
      .reduce((sum, [, metric]) => sum + metric.count, 0);

    const aiTimers = Object.entries(this.metrics.timers)
      .filter(([key]) => key.startsWith('ai.processing_time'));
    
    const averageAiProcessingTime = aiTimers.length > 0
      ? aiTimers.reduce((sum, [, timer]) => sum + timer.averageTime, 0) / aiTimers.length
      : 0;

    const successfulAiRequests = this.getCounter('ai.requests', { success: 'true' })?.count || 0;
    const failedAiRequests = this.getCounter('ai.requests', { success: 'false' })?.count || 0;

    return {
      totalExpenses,
      totalAiRequests,
      totalWebhookMessages,
      averageAiProcessingTime,
      successfulAiRequests,
      failedAiRequests,
    };
  }

  resetMetrics(): void {
    this.metrics = {
      counters: {},
      timers: {},
      gauges: {},
      lastReset: new Date(),
    };
  }

  private buildKey(name: string, tags?: Record<string, string>): string {
    if (!tags || Object.keys(tags).length === 0) {
      return name;
    }
    
    const tagString = Object.entries(tags)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}:${value}`)
      .join(',');
    
    return `${name}[${tagString}]`;
  }
}
