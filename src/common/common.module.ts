import { Module, Global } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';

// Services
import { AppLoggerService } from './services/logger.service';
import { MetricsService } from './services/metrics.service';

// Filters
import { GlobalExceptionFilter } from './filters/global-exception.filter';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    AppLoggerService,
    MetricsService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
  ],
  exports: [
    AppLoggerService,
    MetricsService,
  ],
})
export class CommonModule {}
