import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppCleanModule } from '../../src/app-clean.module';

describe('Clean Architecture Integration (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppCleanModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }));
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/expenses (GET)', () => {
    it('should return expenses list', () => {
      return request(app.getHttpServer())
        .get('/expenses?year=2024&month=3')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('expenses');
          expect(res.body).toHaveProperty('total');
          expect(res.body).toHaveProperty('totalAmount');
          expect(res.body).toHaveProperty('filters');
          expect(Array.isArray(res.body.expenses)).toBe(true);
        });
    });
  });

  describe('/expenses/categories (GET)', () => {
    it('should return available categories', () => {
      return request(app.getHttpServer())
        .get('/expenses/categories')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body).toContain('Alimentos');
          expect(res.body).toContain('Transporte');
        });
    });
  });

  describe('/expenses/years (GET)', () => {
    it('should return available years', () => {
      return request(app.getHttpServer())
        .get('/expenses/years')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });
  });

  describe('/expenses (POST)', () => {
    it('should create a new expense', () => {
      const createExpenseDto = {
        amount: 2500,
        category: 'Alimentos',
        description: 'Compra en el supermercado',
        phoneNumber: '5491123456789',
      };

      return request(app.getHttpServer())
        .post('/expenses')
        .send(createExpenseDto)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('amount', 2500);
          expect(res.body).toHaveProperty('category', 'Alimentos');
          expect(res.body).toHaveProperty('description', 'Compra en el supermercado');
          expect(res.body).toHaveProperty('phoneNumber', '5491123456789');
          expect(res.body).toHaveProperty('date');
          expect(res.body).toHaveProperty('year');
          expect(res.body).toHaveProperty('month');
          expect(res.body).toHaveProperty('isInstallment', false);
        });
    });

    it('should validate expense data', () => {
      const invalidExpenseDto = {
        amount: -100, // Invalid negative amount
        category: 'InvalidCategory',
        description: '',
        phoneNumber: 'invalid-phone',
      };

      return request(app.getHttpServer())
        .post('/expenses')
        .send(invalidExpenseDto)
        .expect(400);
    });
  });

  describe('/webhook (GET)', () => {
    it('should verify webhook with correct parameters', () => {
      return request(app.getHttpServer())
        .get('/webhook')
        .query({
          'hub.mode': 'subscribe',
          'hub.verify_token': process.env.VERIFY_TOKEN || 'test-token',
          'hub.challenge': 'test-challenge',
        })
        .expect(200)
        .expect('test-challenge');
    });

    it('should reject webhook with invalid token', () => {
      return request(app.getHttpServer())
        .get('/webhook')
        .query({
          'hub.mode': 'subscribe',
          'hub.verify_token': 'invalid-token',
          'hub.challenge': 'test-challenge',
        })
        .expect(403);
    });
  });

  describe('/webhook/metrics (GET)', () => {
    it('should return AI processing metrics', () => {
      return request(app.getHttpServer())
        .get('/webhook/metrics')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'success');
          expect(res.body).toHaveProperty('data');
          expect(res.body).toHaveProperty('timestamp');
          expect(res.body.data).toHaveProperty('totalRequests');
          expect(res.body.data).toHaveProperty('successfulRequests');
          expect(res.body.data).toHaveProperty('failedRequests');
        });
    });
  });
});
