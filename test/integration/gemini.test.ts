import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { GeminiService } from '../../src/ai/gemini.service';

describe('GeminiService Integration Tests', () => {
  let service: GeminiService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env',
        }),
      ],
      providers: [GeminiService],
    }).compile();

    service = module.get<GeminiService>(GeminiService);
  });

  // Casos de prueba con frases argentinas típicas
  const testCases = [
    {
      input: "Compré en el chino por 2500",
      expectedAmount: 2500,
      expectedCategory: "Supermercado",
      description: "Compra en almacén argentino"
    },
    {
      input: "Gasté 1500 mangos en el bondi",
      expectedAmount: 1500,
      expectedCategory: "Transporte",
      description: "Viaje en colectivo con jerga argentina"
    },
    {
      input: "Me salió 3400 el super de hoy",
      expectedAmount: 3400,
      expectedCategory: "Supermercado",
      description: "Compra en supermercado"
    },
    {
      input: "Fui al doc, me cobró 8000",
      expectedAmount: 8000,
      expectedCategory: "Salud",
      description: "Consulta médica informal"
    },
    {
      input: "Almorzé en el restaurante, 4500 pesos",
      expectedAmount: 4500,
      expectedCategory: "Comida",
      description: "Comida en restaurante"
    }
  ];

  describe('processNaturalLanguage', () => {
    // Test básico de estructura
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    // Test de mensaje vacío
    it('should handle empty text', async () => {
      const result = await service.processNaturalLanguage('');
      expect(result.success).toBe(false);
      expect(result.error).toContain('vacío');
    });

    // Tests individuales para cada caso
    testCases.forEach((testCase, index) => {
      it(`should process case ${index + 1}: ${testCase.description}`, async () => {
        const result = await service.processNaturalLanguage(testCase.input);
        
        console.log(`Test caso ${index + 1}:`, {
          input: testCase.input,
          result: result
        });

        if (result.success && result.data) {
          expect(result.data.amount).toBe(testCase.expectedAmount);
          expect(result.data.category).toBe(testCase.expectedCategory);
          expect(result.data.isValid).toBe(true);
          expect(result.data.confidence).toBeGreaterThan(0.5);
        } else {
          console.warn(`Test falló para: "${testCase.input}"`, result.error);
          // Para pruebas de integración, podemos ser más permisivos
          // ya que depende de la API externa
        }
      }, 30000); // 30 segundos timeout por la API
    });
  });

  describe('isStructuredMessage', () => {
    it('should detect structured messages', () => {
      const structuredMessages = [
        "4000 pesos una coca cola",
        "2500 transporte",
        "1500 pesos comida"
      ];

      structuredMessages.forEach(message => {
        expect(service.isStructuredMessage(message)).toBe(true);
      });
    });

    it('should not detect natural language as structured', () => {
      const naturalMessages = [
        "Compré en el chino por 2500",
        "Gasté mangos en el bondi",
        "Me salió caro el super"
      ];

      naturalMessages.forEach(message => {
        expect(service.isStructuredMessage(message)).toBe(false);
      });
    });
  });
}); 