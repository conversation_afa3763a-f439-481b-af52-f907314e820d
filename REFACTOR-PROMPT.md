### **Checklist Detallado para la Refactorización del Proyecto **

#### `Fase 1: Preparación y Estructura Base`

- [x] **1. Crear Estructura de Carpetas Base:**
    - [x] Establecer la estructura de carpetas raíz para el módulo `Expense` según la propuesta.
    - [x] Establecer la estructura de carpetas raíz para el módulo `AI` según la propuesta.
    - [x] Establecer la estructura de carpetas raíz para el módulo `Webhook` según la propuesta.

- [x] **2. Implementar Capa de Dominio (Base):**
    - [x] Definir las primeras entidades y *value objects* en las carpetas `domain/` correspondientes de cada módulo.
    - [x] Crear los archivos de interfaces para los repositorios en las carpetas `domain/repositories/` de cada módulo.

#### `Fase 2: Refactorización por Módulos`

##### **<PERSON>ódulo Expense**

- [x] **1. Refactorizar Capa de Dominio:**
    - [x] Crear el archivo `src/expense/domain/entities/expense.entity.ts` y definir la clase `Expense` con sus propiedades y validaciones de negocio.
    - [x] Crear el archivo `src/expense/domain/repositories/expense.repository.ts` y definir la interfaz del repositorio con sus métodos (CRUD, etc.).

- [x] **2. Implementar Capa de Aplicación:**
    - [x] Crear el archivo y la clase para el caso de uso `create-expense.use-case.ts`.
    - [x] Crear el archivo y la clase para el caso de uso `get-expenses.use-case.ts`.
    - [x] Crear los DTOs específicos de la capa de aplicación, como `expense-filter.dto.ts` y `expense-response.dto.ts`.

- [x] **3. Refactorizar Capa de Infraestructura:**
    - [x] Crear el archivo `src/expense/infrastructure/persistence/redis-expense.repository.ts` e implementar la interfaz del repositorio definida en el dominio.
    - [x] Crear el archivo `src/expense/infrastructure/external/whatsapp/whatsapp.service.ts` para abstraer la comunicación con WhatsApp.

- [x] **4. Actualizar Capa de Presentación:**
    - [x] Refactorizar el `expense.controller.ts` para que inyecte y utilice los nuevos casos de uso en lugar de la lógica de negocio directa.
    - [x] Implementar los DTOs de presentación (ej. `create-expense.dto.ts`) con sus correspondientes decoradores de validación.

##### **Módulo AI**

- [x] **1. Refactorizar Capa de Dominio:**
    - [x] Crear las interfaces para los servicios de IA (ej. `IAiProcessor`) dentro de la capa de dominio del módulo.
    - [x] Definir los modelos de dominio (clases o tipos) para estructurar los resultados que devuelve la IA.

- [x] **2. Implementar Capa de Aplicación:**
    - [x] Crear el archivo y la clase para el caso de uso `process-natural-language.use-case.ts`.
    - [x] Asegurarse de que este caso de uso contenga la lógica de aplicación y esté desacoplado de los detalles de la implementación de Gemini.

- [x] **3. Refactorizar Capa de Infraestructura:**
    - [x] Implementar el adaptador específico para la API de Gemini, que cumplirá con la interfaz definida en el dominio.
    - [x] Mover la lógica de `logging` y `métricas` fuera del servicio de Gemini a servicios transversales.

##### **Módulo Webhook**

- [x] **1. Refactorizar Capa de Presentación:**
    - [x] Simplificar el `WebhookController` para que su única función sea recibir la solicitud y delegarla a un caso de uso.
    - [x] Implementar DTOs para realizar la validación del cuerpo de los mensajes entrantes del webhook.

- [x] **2. Implementar Capa de Aplicación:**
    - [x] Crear los casos de uso para procesar los diferentes tipos de mensajes (ej. `process-text-message.use-case.ts`).
    - [x] Mover la lógica de negocio y de enrutamiento de mensajes desde el `WebhookService` a los nuevos casos de uso.

#### `Fase 3: Mejoras Transversales`

- [x] **1. Implementar Manejo Centralizado de Configuración:**
    - [x] Refactorizar la aplicación para utilizar el `ConfigModule` de `@nestjs/config`.
    - [x] Crear esquemas de validación para las variables de entorno (ej. con Joi).

- [x] **2. Implementar Manejo Centralizado de Errores:**
    - [x] Crear filtros de excepción (`Exception Filters`) personalizados para capturar errores de forma global.
    - [x] Estandarizar el formato de las respuestas de error devueltas por la API.

- [x] **3. Implementar Logging y Métricas:**
    - [x] Crear un servicio de `logging` transversal que pueda ser inyectado en cualquier parte de la aplicación.
    - [x] Separar la recolección de métricas de la lógica de negocio y centralizarla en un servicio o módulo dedicado.