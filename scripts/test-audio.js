const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');
const fs = require('fs');
const path = require('path');

async function testAudioProcessing() {
  console.log('🎤 Probando procesamiento de audio con Gemini...');
  
  try {
    // Crear la aplicación NestJS
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Obtener servicios necesarios
    const { GeminiService } = require('../dist/ai/gemini.service');
    const geminiService = app.get(GeminiService);
    
    console.log('✅ Servicios cargados correctamente');
    
    // Simular un buffer de audio (en un caso real vendría de WhatsApp)
    // Por ahora, creamos un buffer vacío para probar la validación
    console.log('📝 Probando con audio vacío (para validación)...');
    
    const emptyBuffer = Buffer.alloc(0);
    const result1 = await geminiService.processAudioMessage(emptyBuffer, 'audio/ogg');
    
    console.log('Resultado con audio vacío:', result1);
    
    // Simular un buffer de audio con datos (aunque no sea audio real)
    console.log('📝 Probando con buffer simulado...');
    
    const simulatedAudioBuffer = Buffer.from('Este es un buffer simulado de audio', 'utf-8');
    const result2 = await geminiService.processAudioMessage(simulatedAudioBuffer, 'audio/ogg');
    
    console.log('Resultado con buffer simulado:', result2);
    
    console.log('\n🎯 Pruebas completadas');
    console.log('Para probar con audio real, necesitarías:');
    console.log('1. Un archivo de audio real (MP3, OGG, WAV, etc.)');
    console.log('2. Leerlo como Buffer: Buffer.from(fs.readFileSync("audio.mp3"))');
    console.log('3. Llamar a processAudioMessage con el buffer y mimeType correcto');
    
    await app.close();
    
  } catch (error) {
    console.error('❌ Error en las pruebas de audio:', error);
  }
}

async function testWhatsAppAudioDownload() {
  console.log('\n📱 Probando descarga de audio de WhatsApp...');
  
  try {
    const app = await NestFactory.createApplicationContext(AppModule);
    const { WhatsAppService } = require('../dist/expense/whatsapp.service');
    const whatsappService = app.get(WhatsAppService);
    
    console.log('✅ WhatsAppService cargado');
    
    // Probar conversión de tipos MIME
    const testBuffer = Buffer.from('test audio data');
    
    const oggResult = await whatsappService.convertAudioForGemini(testBuffer, 'audio/ogg; codecs=opus');
    console.log('Conversión OGG:', oggResult.mimeType);
    
    const mp3Result = await whatsappService.convertAudioForGemini(testBuffer, 'audio/mpeg');
    console.log('Conversión MP3:', mp3Result.mimeType);
    
    const unknownResult = await whatsappService.convertAudioForGemini(testBuffer, 'audio/unknown');
    console.log('Conversión desconocida:', unknownResult.mimeType);
    
    console.log('✅ Pruebas de conversión completadas');
    
    await app.close();
    
  } catch (error) {
    console.error('❌ Error en pruebas de WhatsApp:', error);
  }
}

async function main() {
  await testAudioProcessing();
  await testWhatsAppAudioDownload();
}

main().catch(console.error); 