const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testRedisConnection() {
  console.log('🔍 Probando conexión a Redis y guardado de gastos...');
  
  try {
    // Crear la aplicación NestJS
    const app = await NestFactory.createApplicationContext(AppModule);
    
    console.log('✅ Aplicación NestJS creada correctamente');
    
    // Intentar obtener el servicio usando diferentes métodos
    let expenseService;
    try {
      expenseService = app.get('ExpenseService');
      console.log('✅ ExpenseService obtenido por nombre');
    } catch (error) {
      console.log('❌ No se pudo obtener ExpenseService por nombre:', error.message);
      
      // Intentar obtener por clase
      try {
        const { ExpenseService } = require('../dist/expense/expense.service');
        expenseService = app.get(ExpenseService);
        console.log('✅ ExpenseService obtenido por clase');
      } catch (classError) {
        console.log('❌ No se pudo obtener ExpenseService por clase:', classError.message);
        throw new Error('No se pudo obtener ExpenseService');
      }
    }
    
    // Crear un gasto de prueba
    const testExpense = {
      id: '',
      date: new Date().toISOString(),
      amount: 2500,
      category: 'Supermercado',
      description: 'Prueba de conexión Redis',
      phoneNumber: '541234567890'
    };
    
    console.log('📝 Gasto de prueba:', testExpense);
    
    // Intentar guardar el gasto
    console.log('💾 Guardando gasto en Redis...');
    const saved = await expenseService.save(testExpense);
    
    if (saved) {
      console.log('✅ Gasto guardado exitosamente en Redis');
      
      // Intentar recuperar gastos del mes actual
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      
      console.log(`📊 Buscando gastos de ${month}/${year}...`);
      const expenses = await expenseService.getByYearAndMonth(year, month);
      
      console.log(`📋 Gastos encontrados: ${expenses.length}`);
      expenses.forEach((expense, index) => {
        console.log(`   ${index + 1}. $${expense.amount} - ${expense.category} - ${expense.description}`);
      });
      
    } else {
      console.log('❌ Error al guardar el gasto en Redis');
    }
    
    await app.close();
    console.log('\n🏁 Prueba completada');
    
  } catch (error) {
    console.error('❌ Error durante la prueba:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  testRedisConnection().catch(console.error);
}

module.exports = { testRedisConnection }; 