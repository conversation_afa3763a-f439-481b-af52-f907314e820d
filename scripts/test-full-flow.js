const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testFullFlow() {
  console.log('🔄 Probando flujo completo de procesamiento de gastos...');
  
  try {
    // Crear la aplicación NestJS
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Obtener servicios necesarios
    const { WebhookService } = require('../dist/webhook/webhook.service');
    const { WhatsAppService } = require('../dist/expense/whatsapp.service');
    const { ExpenseService } = require('../dist/expense/expense.service');
    
    const webhookService = app.get(WebhookService);
    const whatsappService = app.get(WhatsAppService);
    const expenseService = app.get(ExpenseService);
    
    console.log('✅ Servicios obtenidos correctamente');
    
    // Simular un mensaje de WhatsApp con lenguaje natural
    const testPhoneNumber = '************';
    const testMessage = 'Compré en el chino por 3500';
    
    console.log(`📱 Simulando mensaje: "${testMessage}" desde ${testPhoneNumber}`);
    
    // Simular el cuerpo del webhook de WhatsApp
    const webhookBody = {
      object: 'whatsapp_business_account',
      entry: [{
        changes: [{
          value: {
            messages: [{
              from: testPhoneNumber,
              type: 'text',
              text: {
                body: testMessage
              }
            }]
          }
        }]
      }]
    };
    
    // Procesar el mensaje
    console.log('🔄 Procesando mensaje con webhook...');
    await webhookService.processWhatsAppMessage(webhookBody);
    
    // Esperar un poco para que se procese
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Verificar que hay un gasto pendiente
    console.log('🔍 Verificando gastos pendientes...');
    const pendingExpenses = whatsappService.pendingExpenses;
    console.log(`Gastos pendientes: ${pendingExpenses.size}`);
    
    if (pendingExpenses.size > 0) {
      // Obtener el primer gasto pendiente
      const [pendingId, expense] = pendingExpenses.entries().next().value;
      console.log('📋 Gasto pendiente encontrado:', { pendingId, expense });
      
      // Simular confirmación del botón
      console.log('✅ Simulando confirmación del gasto...');
      const confirmButtonId = `confirm_${pendingId}`;
      
      // Simular webhook de confirmación de botón
      const buttonWebhookBody = {
        object: 'whatsapp_business_account',
        entry: [{
          changes: [{
            value: {
              messages: [{
                from: testPhoneNumber,
                type: 'interactive',
                interactive: {
                  type: 'button_reply',
                  button_reply: {
                    id: confirmButtonId
                  }
                }
              }]
            }
          }]
        }]
      };
      
      // Procesar la confirmación
      await webhookService.processWhatsAppMessage(buttonWebhookBody);
      
      // Esperar un poco
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Verificar que el gasto se guardó
      console.log('🔍 Verificando que el gasto se guardó...');
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      
      const savedExpenses = await expenseService.getByYearAndMonth(year, month, undefined, testPhoneNumber);
      console.log(`💾 Gastos guardados encontrados: ${savedExpenses.length}`);
      
      savedExpenses.forEach((expense, index) => {
        console.log(`   ${index + 1}. $${expense.amount} - ${expense.category} - ${expense.description}`);
      });
      
      if (savedExpenses.length > 0) {
        console.log('🎉 ¡Flujo completo exitoso!');
      } else {
        console.log('❌ El gasto no se guardó correctamente');
      }
      
    } else {
      console.log('❌ No se creó ningún gasto pendiente');
    }
    
    await app.close();
    console.log('\n🏁 Prueba de flujo completo terminada');
    
  } catch (error) {
    console.error('❌ Error durante la prueba:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  testFullFlow().catch(console.error);
}

module.exports = { testFullFlow }; 