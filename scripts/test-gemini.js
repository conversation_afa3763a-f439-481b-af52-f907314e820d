const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testGemini() {
  console.log('🚀 Iniciando prueba de Gemini...');
  
  try {
    // Crear la aplicación NestJS
    const app = await NestFactory.createApplicationContext(AppModule);
    const geminiService = app.get('GeminiService');
    
    console.log('✅ Servicio Gemini cargado correctamente');
    
    // Casos de prueba
    const testCases = [
      "Compré en el chino por 2500",
      "Gasté 1500 mangos en el bondi", 
      "Me salió 3400 el super de hoy",
      "<PERSON><PERSON> al doc, me cobró 8000",
      "4000 pesos una coca cola" // caso estructurado
    ];
    
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`\n📝 Prueba ${i + 1}: "${testCase}"`);
      
      try {
        // Verificar si es estructurado
        const isStructured = geminiService.isStructuredMessage(testCase);
        console.log(`   Estructurado: ${isStructured ? '✅' : '❌'}`);
        
        if (!isStructured) {
          // Procesar con Gemini
          const result = await geminiService.processNaturalLanguage(testCase);
          
          if (result.success && result.data) {
            console.log(`   ✅ Procesado exitosamente:`);
            console.log(`      💰 Monto: $${result.data.amount}`);
            console.log(`      📁 Categoría: ${result.data.category}`);
            console.log(`      📝 Descripción: ${result.data.description}`);
            console.log(`      🎯 Confianza: ${result.data.confidence}`);
          } else {
            console.log(`   ❌ Error: ${result.error}`);
          }
        } else {
          console.log(`   ℹ️  Mensaje estructurado - se procesaría con lógica anterior`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error en prueba: ${error.message}`);
      }
    }
    
    await app.close();
    console.log('\n🏁 Pruebas completadas');
    
  } catch (error) {
    console.error('❌ Error al inicializar la aplicación:', error.message);
    process.exit(1);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  testGemini().catch(console.error);
}

module.exports = { testGemini }; 