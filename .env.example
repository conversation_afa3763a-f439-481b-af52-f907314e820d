# =================================
# BotGastos Clean Architecture - Environment Variables
# =================================

# =================================
# CRITICAL VARIABLES (Required for basic functionality)
# =================================

# Redis Database Configuration (REQUIRED for data persistence)
# Get these from Upstash Redis: https://upstash.com/
UPSTASH_REDIS_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_TOKEN=your_redis_token_here

# =================================
# WHATSAPP INTEGRATION (Required for webhook functionality)
# =================================

# WhatsApp Business API Configuration
# Get these from Meta for Developers: https://developers.facebook.com/
WHATSAPP_NUMBER_ID=your_whatsapp_number_id
WHATSAPP_TOKEN=your_whatsapp_access_token

# Webhook verification token (you can set this to any secure string)
VERIFY_TOKEN=your_secure_verify_token_here

# =================================
# AI INTEGRATION (Required for natural language processing)
# =================================

# Google Gemini API Key
# Get this from Google AI Studio: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# =================================
# OPTIONAL CONFIGURATION
# =================================

# Server Configuration
PORT=3000
NODE_ENV=development

# Logging Configuration
LOG_LEVEL=info

# Application Limits
MAX_AUDIO_SIZE_MB=10
MIN_CONFIDENCE_THRESHOLD=0.6
MAX_EXPENSE_AMOUNT=1000000

# =================================
# DEVELOPMENT/TESTING VALUES
# =================================
# For development and testing, you can use these placeholder values:
# UPSTASH_REDIS_URL=redis://localhost:6379
# UPSTASH_REDIS_TOKEN=local_development_token
# WHATSAPP_NUMBER_ID=test_number_id
# WHATSAPP_TOKEN=test_token
# VERIFY_TOKEN=test_verify_token
# GEMINI_API_KEY=test_api_key

# =================================
# FUNCTIONALITY MATRIX
# =================================
# 
# ✅ WORKS WITHOUT EXTERNAL SERVICES:
# - GET /expenses/categories/available
# - Basic application startup
# - Health checks
# - Validation and error handling
# 
# 🔶 REQUIRES REDIS (UPSTASH_REDIS_URL + UPSTASH_REDIS_TOKEN):
# - GET /expenses (listing expenses)
# - POST /expenses (creating expenses)
# - All expense CRUD operations
# - Data persistence
# 
# 🔶 REQUIRES WHATSAPP (WHATSAPP_NUMBER_ID + WHATSAPP_TOKEN + VERIFY_TOKEN):
# - POST /webhook (receiving WhatsApp messages)
# - GET /webhook (webhook verification)
# - Sending WhatsApp messages
# 
# 🔶 REQUIRES GEMINI AI (GEMINI_API_KEY):
# - Natural language processing
# - Audio message transcription
# - AI-powered expense extraction
# - GET /webhook/metrics (AI processing metrics)
# 
# =================================
