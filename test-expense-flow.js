// Script para probar el flujo completo de gastos con confirmación

const testExpenseFlow = {
  // 1. Mensaje inicial del usuario
  userMessage: {
    object: "whatsapp_business_account",
    entry: [
      {
        id: "ENTRY_ID",
        changes: [
          {
            value: {
              messaging_product: "whatsapp",
              metadata: {
                display_phone_number: "***********",
                phone_number_id: "PHONE_NUMBER_ID"
              },
              contacts: [
                {
                  profile: {
                    name: "Test User"
                  },
                  wa_id: "*************"
                }
              ],
              messages: [
                {
                  from: "*************",
                  id: "wamid.MESSAGE_ID_1",
                  timestamp: "**********",
                  text: {
                    body: "Me acabo de comprar la camiseta de boca a 90000 pesos"
                  },
                  type: "text"
                }
              ]
            },
            field: "messages"
          }
        ]
      }
    ]
  },

  // 2. Respuesta de confirmación del usuario (simulando click en botón)
  confirmationResponse: {
    object: "whatsapp_business_account",
    entry: [
      {
        id: "ENTRY_ID",
        changes: [
          {
            value: {
              messaging_product: "whatsapp",
              metadata: {
                display_phone_number: "***********",
                phone_number_id: "PHONE_NUMBER_ID"
              },
              messages: [
                {
                  from: "*************",
                  id: "wamid.MESSAGE_ID_2",
                  timestamp: "**********",
                  type: "interactive",
                  interactive: {
                    type: "button_reply",
                    button_reply: {
                      id: "confirm_pending_**********_abc1", // Este ID debe coincidir con el generado
                      title: "Confirmar"
                    }
                  }
                }
              ]
            },
            field: "messages"
          }
        ]
      }
    ]
  }
};

async function testStep(stepName, payload) {
  console.log(`\n=== ${stepName} ===`);
  console.log('Payload:', JSON.stringify(payload, null, 2));
  
  try {
    const response = await fetch('http://localhost:3000/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    } else {
      console.log('✅ Success');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function testCompleteFlow() {
  console.log('🧪 Testing complete expense flow...');
  console.log('Expected flow:');
  console.log('1. User: "Me acabo de comprar la camiseta de boca a 90000 pesos"');
  console.log('2. Bot: "Confirmar gasto" + confirmation buttons');
  console.log('3. User: Click "Confirmar"');
  console.log('4. Bot: "✅ Gasto guardado exitosamente..."');
  
  // Esperar un poco para asegurar que el servidor esté listo
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Paso 1: Enviar mensaje de gasto
  await testStep('Step 1: User sends expense message', testExpenseFlow.userMessage);
  
  console.log('\n⏳ Waiting 3 seconds before sending confirmation...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Paso 2: Simular confirmación (nota: el ID real será diferente)
  console.log('\n📝 Note: In real scenario, you would click the "Confirmar" button in WhatsApp');
  console.log('The button ID would be generated dynamically by the server');
  
  console.log('\n✅ Test completed!');
  console.log('\nTo test the confirmation step:');
  console.log('1. Check the server logs for the generated pending ID');
  console.log('2. Use that ID to create a confirmation message');
  console.log('3. Or test directly in WhatsApp with real buttons');
}

// Función para probar solo el mensaje inicial
async function testInitialMessage() {
  console.log('🧪 Testing initial expense message...');
  await testStep('Initial expense message', testExpenseFlow.userMessage);
}

// Función para crear un mensaje de confirmación con un ID específico
function createConfirmationMessage(pendingId) {
  return {
    object: "whatsapp_business_account",
    entry: [
      {
        id: "ENTRY_ID",
        changes: [
          {
            value: {
              messaging_product: "whatsapp",
              metadata: {
                display_phone_number: "***********",
                phone_number_id: "PHONE_NUMBER_ID"
              },
              messages: [
                {
                  from: "*************",
                  id: "wamid.MESSAGE_ID_2",
                  timestamp: "**********",
                  type: "interactive",
                  interactive: {
                    type: "button_reply",
                    button_reply: {
                      id: `confirm_${pendingId}`,
                      title: "Confirmar"
                    }
                  }
                }
              ]
            },
            field: "messages"
          }
        ]
      }
    ]
  };
}

// Ejecutar si se llama directamente
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args[0] === 'initial') {
    testInitialMessage().catch(console.error);
  } else if (args[0] === 'confirm' && args[1]) {
    const pendingId = args[1];
    const confirmMessage = createConfirmationMessage(pendingId);
    testStep(`Confirmation with ID: ${pendingId}`, confirmMessage).catch(console.error);
  } else {
    testCompleteFlow().catch(console.error);
  }
}

module.exports = { testExpenseFlow, testStep, testCompleteFlow, createConfirmationMessage };
