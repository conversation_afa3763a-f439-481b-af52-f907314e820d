// Script para probar el webhook con diferentes tipos de mensajes de WhatsApp

const testMessages = {
  // Mensaje de texto simple
  textMessage: {
    object: "whatsapp_business_account",
    entry: [
      {
        id: "ENTRY_ID",
        changes: [
          {
            value: {
              messaging_product: "whatsapp",
              metadata: {
                display_phone_number: "***********",
                phone_number_id: "PHONE_NUMBER_ID"
              },
              contacts: [
                {
                  profile: {
                    name: "Test User"
                  },
                  wa_id: "*************"
                }
              ],
              messages: [
                {
                  from: "*************",
                  id: "wamid.MESSAGE_ID",
                  timestamp: "**********",
                  text: {
                    body: "2500 almuerzo en el restaurant"
                  },
                  type: "text"
                }
              ]
            },
            field: "messages"
          }
        ]
      }
    ]
  },

  // Mensaje con solo contactos (sin mensajes)
  contactsOnly: {
    object: "whatsapp_business_account",
    entry: [
      {
        id: "ENTRY_ID",
        changes: [
          {
            value: {
              messaging_product: "whatsapp",
              metadata: {
                display_phone_number: "***********",
                phone_number_id: "PHONE_NUMBER_ID"
              },
              contacts: [
                {
                  profile: {
                    name: "Test User"
                  },
                  wa_id: "*************"
                }
              ]
            },
            field: "messages"
          }
        ]
      }
    ]
  },

  // Status update
  statusUpdate: {
    object: "whatsapp_business_account",
    entry: [
      {
        id: "ENTRY_ID",
        changes: [
          {
            value: {
              messaging_product: "whatsapp",
              metadata: {
                display_phone_number: "***********",
                phone_number_id: "PHONE_NUMBER_ID"
              },
              statuses: [
                {
                  id: "wamid.MESSAGE_ID",
                  status: "delivered",
                  timestamp: "**********",
                  recipient_id: "*************"
                }
              ]
            },
            field: "messages"
          }
        ]
      }
    ]
  }
};

async function testWebhook(messageType) {
  const message = testMessages[messageType];
  
  console.log(`\n=== Testing ${messageType} ===`);
  console.log('Payload:', JSON.stringify(message, null, 2));
  
  try {
    const response = await fetch('http://localhost:3000/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    });
    
    console.log('Response status:', response.status);
    console.log('Response:', response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Error testing webhook:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Testing WhatsApp Webhook with different message types...');
  
  // Esperar un poco para asegurar que el servidor esté listo
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testWebhook('textMessage');
  await testWebhook('contactsOnly');
  await testWebhook('statusUpdate');
  
  console.log('\n✅ All tests completed!');
}

// Ejecutar si se llama directamente
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testMessages, testWebhook, runTests };
